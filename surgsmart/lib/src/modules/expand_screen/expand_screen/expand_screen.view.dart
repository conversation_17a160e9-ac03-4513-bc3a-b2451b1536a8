import 'package:app_foundation/app_foundation.dart';
import 'package:blur/blur.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:medias_kit/monitor/monitor_view.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/models/screen_status.model.dart';
import 'package:surgsmart/src/modules/expand_screen/expand_screen/widgets/status_card.widget.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/tracking/widgets/tracking_card.widget.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/widgets/drawing_board.widget.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/widgets/drawing_rectangle.widget.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/widgets/corner_container.widget.dart';
import '../../../models/http/room.model.dart';
import 'expand_screen.controller.dart';

/// 所属模块: expand_screen
///
/// 扩展屏幕显示
class ExpandScreenView extends AppView<ExpandScreenController> {
  const ExpandScreenView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: controller.build(
        onSuccess: onSuccess,
        onFailure: onFailure,
        onLoading: onLoading,
        onEmpty: onEmpty,
      ),
    );
  }

  Widget onSuccess(BuildContext context, ApiModel data) {
    return ValueListenableBuilder(
      valueListenable: controller.id,
      builder: (context, id, child) {
        final status = controller.statusInfo.value;
        print(">>>>>>>>>>>>>> ${id % 4}");
        return status.surgeryIsStart
            ? makeSurgeryWidget(status)
            : Image.asset(R.image.no_picture().assetName, fit: BoxFit.fitWidth);
      },
    );
  }

  Widget makeSurgeryWidget(ExpandScreenStatusInfo status) {
    return Column(
      children: [
        Expanded(
          child: Stack(
            children: [
              makeMonitorLayer(status),
              // if (!status.hasSignal) noSignalDialog(),
              // if (status.isPlayback) ...makePlaybackLayer(status),
              // makeTrackingLayer(),
              // makeLeftTopTitle(status),
              // if (!status.isPlayback) makeTopBar(status),
            ],
          ),
        ),
        makeBottomStatusBar(status),
      ],
    );
  }

  Widget makeTopBar(ExpandScreenStatusInfo status) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 32.r, vertical: 32.r),
      child: Row(
        children: [
          Spacer(),
          // 手术协同医生人数
          Visibility(
            visible: status.networkConnected && status.doctors.isNotEmpty,
            child: Padding(
              padding: EdgeInsets.only(right: 18.w),
              child: CornerContainer(
                showIndicator: false,
                backgroundColor: Colors.transparent,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image(image: R.image.rtc_live(), width: 50.r, height: 50.r),
                    Text(
                      " ${status.doctors.length}",
                      style: TextStyle(
                        fontSize: 48.sp,
                        fontWeight: FontWeight.w500,
                        height: 1,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // 直播在线人数
          Visibility(
            visible: status.networkConnected && status.liveUserCount != null,
            child: Padding(
              padding: EdgeInsets.only(right: 18.w),
              child: CornerContainer(
                showIndicator: false,
                backgroundColor: Colors.transparent,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Image(image: R.image.rtm_live(), width: 50.r, height: 50.r),
                    Text(
                      " ${status.liveUserCount}",
                      style: TextStyle(
                        fontSize: 48.sp,
                        fontWeight: FontWeight.w500,
                        height: 1,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // RTC
          if (status.hasSignal)
            Padding(
              padding: EdgeInsets.only(right: 18.w),
              child: CornerContainer(
                backgroundColor: Colors.transparent,
                showIndicator: false,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                          width: 23.r,
                          height: 23.r,
                          decoration: BoxDecoration(
                            color: const Color(0xFFD4353F),
                            borderRadius: BorderRadius.circular(11.5.r),
                          ),
                        )
                        .animate(
                          onComplete: (controller) {
                            controller.repeat(reverse: true);
                          },
                        )
                        .fade(duration: 500.ms),
                    SizedBox(
                      height: 50.h,
                      child: Text(
                        " REC",
                        style: TextStyle(
                          fontSize: 45.sp,
                          fontWeight: FontWeight.w500,
                          height: 1,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          // 网络
          CornerContainer(
            backgroundColor: Colors.transparent,
            showIndicator: false,
            child: Row(
              children: [
                SvgPicture.asset(
                  AppContext.share
                      .getNetWorkStatusIcon(networkType: status.networkType)
                      .keyName,
                  width: 50.r,
                  height: 50.r,
                ),
                Visibility(
                  visible: status.networkConnected,
                  child: Text.rich(
                    TextSpan(
                      text: " ${status.videoBitrate}",
                      style: TextStyle(
                        fontSize: 35.sp,
                        fontWeight: FontWeight.w500,
                        height: 1,
                      ),
                      children: [
                        TextSpan(
                          text: "kb/s",
                          style: TextStyle(
                            fontSize: 25.sp,
                            fontWeight: FontWeight.w500,
                            height: 1,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget makeLeftTopTitle(ExpandScreenStatusInfo status) {
    return getLabel(status).frosted(
      frostColor: const Color(0x33333333),
      blur: 50,
      borderRadius: BorderRadius.only(bottomRight: Radius.circular(10.r)),
      padding: EdgeInsets.all(status.isPlayback ? 28.r : 24.r),
    );
  }

  Widget makeMonitorBlurLayer(ExpandScreenStatusInfo status) {
    return Visibility(
      visible: status.videoIsFuzzy,
      child: Positioned.fill(
        child: const SizedBox.shrink().frosted(
          frostColor: Colors.black,
          blur: 50,
          borderRadius: BorderRadius.circular(20.r),
          padding: const EdgeInsets.symmetric(
            vertical: double.infinity,
            horizontal: double.infinity,
          ),
        ),
      ),
    );
  }

  Widget makeSecondaryVideoLayer(ExpandScreenStatusInfo status) {
    final scale = controller.trackingInfo.value?.eventFlags == 0 ? 1 : 0.5;
    return Visibility(
      visible: status.externalVideoDevice != null,
      child: Positioned(
        width: 576.w * scale,
        height: 324.h * scale,
        bottom: 0,
        right: 0,
        child:
            controller.monitor2Controller == null
                ? const SizedBox.shrink()
                : MonitorView(monitor: controller.monitor2Controller!),
      ),
    );
  }

  List<Widget> makePlaybackLayer(ExpandScreenStatusInfo status) {
    return [
      Positioned.fill(
        child: Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.black,
          alignment: Alignment.center,
          child: Stack(
            children: [
              // Texture(textureId: controller.playbackTextureId),
              Padding(
                padding: EdgeInsets.only(top: 24.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.warning, color: Color(0xFFFAAD14), size: 48.r),
                    Text("非腔镜实时画面", style: TextStyle(fontSize: 30.sp)),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      if (status.playbackType == PlaybackType.surgPlay)
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          child: Container(
            padding: EdgeInsets.only(bottom: 24.h),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Colors.transparent, Colors.black],
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  status.processDuration?.split('/')[0] ?? '',
                  style: TextStyle(fontSize: 40.sp, color: Colors.white),
                ),
                Text(
                  '/${status.processDuration?.split('/')[1] ?? ''}',
                  style: TextStyle(fontSize: 36.sp, color: Color(0x73FFFFFF)),
                ),
              ],
            ),
          ),
        ),

      if (status.playbackType == PlaybackType.bleedPlay)
        ValueListenableBuilder(
          valueListenable: controller.bleedingPoints,
          builder: (context, points, child) {
            return points.isEmpty
                ? SizedBox.shrink()
                : DrawingRectangle(points: points);
          },
        ),
    ];
  }

  Widget makeGraphicsLayer() {
    return ValueListenableBuilder(
      valueListenable: controller.graphics,
      builder: (context, graphics, child) {
        /// 专家绘线
        return Positioned.fill(
          child: DrawingBoard(
            userGraphics: graphics,
            showSmallFinger:
                controller.trackingInfo.value != null &&
                controller.trackingInfo.value!.eventFlags > 0,
          ),
        );
      },
    );
  }

  Widget makeTrackingLayer() {
    return ValueListenableBuilder(
      valueListenable: controller.trackingInfo,
      builder: (context, value, child) {
        if (value == null || value.onlyEventFlags) {
          return const SizedBox.shrink();
        }
        return Stack(
          children: [
            Builder(
              builder: (context) {
                if (value.eventFlags > 0) {
                  return Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Expanded(
                          child: AspectRatio(
                            aspectRatio: 16 / 9,
                            // child: Texture(
                            //   textureId: controller.trackingTextureId,
                            // ),
                          ),
                        ),
                        Expanded(child: AspectRatio(aspectRatio: 16 / 9)),
                      ],
                    ),
                  );
                } else {
                  return Center(
                    child: AspectRatio(
                      aspectRatio: 16 / 9,
                      // child: Texture(textureId: controller.trackingTextureId),
                    ),
                  );
                }
              },
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Transform.translate(
                offset: Offset(0, 50.h),
                child: Transform.scale(
                  scale: 0.4,
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Expanded(
                        child: TrackingCard(
                          title: "动脉走形识别",
                          color: const Color(0xFF1AFF2B),
                          bgColor: Colors.black.withValues(alpha: 0.7),
                          defaultBgColor: Colors.black.withValues(alpha: 0.45),
                          isActive: value.arteryTracing,
                        ),
                      ),
                      SizedBox(width: 24.w),
                      Expanded(
                        child: TrackingCard(
                          title: "动脉识别",
                          color: const Color(0xFFFF4D4F),
                          bgColor: Colors.black.withValues(alpha: 0.7),
                          defaultBgColor: Colors.black.withValues(alpha: 0.45),
                          isActive: value.arteryIdentification,
                        ),
                      ),
                      SizedBox(width: 24.w),
                      Expanded(
                        child: TrackingCard(
                          title: "静脉识别",
                          color: const Color(0xFF42A1FF),
                          bgColor: Colors.black.withValues(alpha: 0.7),
                          defaultBgColor: Colors.black.withValues(alpha: 0.45),
                          isActive: value.veinIdentification,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 24.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.warning, color: Color(0xFFFAAD14), size: 48.r),
                  Text("非腔镜实时画面", style: TextStyle(fontSize: 30.sp)),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget makeBottomStatusBar(ExpandScreenStatusInfo status) {
    return Container(
      color: Color(0xFF0E1826),
      padding: EdgeInsets.all(18.h),
      child: Center(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          spacing: 25.w,
          children: [
            StatusCard(
              defaultImage: R.image.screen_show_original(),
              activeImage: R.image.screen_show_original_active(),
              title: status.doctors.isNotEmpty ? "协同手术" : "实时画面",
              desc:
                  status.externalVideoDevice?.isNotEmpty == true ? "双镜" : null,
              isActive: controller.id.value % 4 == 0,
              // isActive:
              //     !status.isPlayback &&
              //     (controller.trackingInfo.value == null ||
              //         controller.trackingInfo.value?.onlyEventFlags == true),
            ),
            StatusCard(
              defaultImage: R.image.screen_show_playback(),
              activeImage: R.image.screen_show_playback_active(),
              title: "智能回放",
              isActive: controller.id.value % 4 == 1,
              // isActive:
              //     status.isPlayback &&
              //     status.playbackType == PlaybackType.surgPlay,
            ),
            StatusCard(
              defaultImage: R.image.screen_show_ai(),
              activeImage: R.image.screen_show_ai_active(),
              title: "AI辅助",
              desc:
                  status.isPlayback &&
                          status.playbackType == PlaybackType.bleedPlay
                      ? "出血点智能定位"
                      : "解剖结构定位",
              isActive: controller.id.value % 4 == 2,
              // isActive:
              //     (controller.trackingInfo.value?.onlyEventFlags == false &&
              //         !status.isPlayback) ||
              //     (status.isPlayback &&
              //         status.playbackType == PlaybackType.bleedPlay),
            ),
            StatusCard(
              defaultImage: R.image.screen_show_model(),
              activeImage: R.image.screen_show_model_active(),
              title: "术前影像",
              desc: "三维重建",
              isActive: controller.id.value % 4 == 3,
              //   isActive:
              //       !status.isPlayback &&
              //       controller.trackingInfo.value != null &&
              //       controller.trackingInfo.value?.eventFlags != 0,
            ),
          ],
        ),
      ),
    );
  }

  Widget makeMonitorLayer(ExpandScreenStatusInfo status) {
    return ValueListenableBuilder(
      valueListenable: controller.trackingInfo,
      builder: (context, value, child) {
        if (value != null && value.eventFlags > 0) {
          return Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Expanded(
                  child: AspectRatio(
                    aspectRatio: 16 / 9,
                    child: makeMonitorView(status),
                  ),
                ),
                Expanded(
                  child: AspectRatio(
                    aspectRatio: 16 / 9,
                    // child: Texture(textureId: controller.modelTextureId),
                  ),
                ),
              ],
            ),
          );
        } else {
          return Center(child: makeMonitorView(status));
        }
      },
    );
  }

  Widget makeMonitorView(ExpandScreenStatusInfo status) {
    return Stack(
      children: [
        controller.monitorController == null
            ? const SizedBox.shrink()
            : MonitorView(monitor: controller.monitorController!),
        makeMonitorBlurLayer(status),
        makeGraphicsLayer(),
        makeSecondaryVideoLayer(status),
      ],
    );
  }

  Widget noSignalDialog() {
    return Positioned.fill(
      child: Container(
        color: Colors.black.withValues(alpha: 0.6),
        width: double.infinity,
        height: double.infinity,
        child: Center(
          child: Container(
            width: 550,
            height: 284,
            decoration: BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.all(Radius.circular(12)),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.5),
                width: 2,
              ),
            ),
            child: Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFFD4353F),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(10),
                      topRight: Radius.circular(10),
                    ),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.warning_amber_rounded,
                        color: Colors.white,
                        size: 40,
                      ),
                      SizedBox(width: 12),
                      Text(
                        '未收到腔镜信号',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 28,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Center(
                    child: Text(
                      '请确认腔镜正常开启\n且视频连接线两端插稳',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 36,
                        fontWeight: FontWeight.w400,
                        height: 1.5,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }

  String getTitle(ExpandScreenStatusInfo status) {
    if (status.isPlayback && status.playbackType == PlaybackType.surgPlay) {
      return '术中实时回放';
    } else if (status.isPlayback &&
        status.playbackType == PlaybackType.bleedPlay) {
      return '出血点智能定位';
    }
    return status.procedureName ?? '未知术式';
  }

  Widget getLabel(ExpandScreenStatusInfo status) {
    return Image(
      image: R.image.logo_variant(),
      fit: BoxFit.contain,
      width: 279.w,
      height: 64.h,
    );
    // return status.isPlayback
    //     ? Text(
    //       getTitle(status),
    //       style: TextStyle(fontSize: 40.sp, fontWeight: FontWeight.w500),
    //     )
    //     : Image(
    //       image: R.image.logo_variant(),
    //       fit: BoxFit.contain,
    //       width: 279.w,
    //       height: 64.h,
    //     );
  }
}

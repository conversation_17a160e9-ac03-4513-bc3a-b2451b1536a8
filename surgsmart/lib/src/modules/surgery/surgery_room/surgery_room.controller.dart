import 'dart:async';
import 'dart:collection';
import 'dart:convert';
import 'dart:ffi' as ffi;
import 'dart:io';
import 'dart:ui' as ui;
import 'package:ffi/ffi.dart';
import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:lpinyin/lpinyin.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:medias_kit/core/medias_kit_ffi.dart' as ffi;
import 'package:medias_kit/medias_kit.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/apis/api.dart';
import 'package:surgsmart/src/apis/http/cos.api.dart';
import 'package:surgsmart/src/apis/http/live.api.dart';
import 'package:surgsmart/src/apis/http/local.api.dart';
import 'package:surgsmart/src/apis/http/room.api.dart';
import 'package:surgsmart/src/apis/http/surgery.api.dart';
import 'package:surgsmart/src/apis/mqtt/device_ai.api.dart';
import 'package:surgsmart/src/apis/mqtt/device_control.api.dart';
import 'package:surgsmart/src/apis/mqtt/device_status.api.dart';
import 'package:surgsmart/src/apis/mqtt/track.api.dart';
import 'package:surgsmart/src/apis/mqtt/whiteboard.api.dart';
import 'package:surgsmart/src/models/client_msg_type.model.dart';
import 'package:surgsmart/src/models/device_setting_model.dart';
import 'package:surgsmart/src/models/event_bus.model.dart';
import 'package:surgsmart/src/models/http/auth.model.dart';
import 'package:surgsmart/src/models/http/cos.model.dart';
import 'package:surgsmart/src/models/http/doctor.model.dart';
import 'package:surgsmart/src/models/http/live.model.dart';
import 'package:surgsmart/src/models/http/room.model.dart';
import 'package:surgsmart/src/models/http/surgery.model.dart';
import 'package:surgsmart/src/models/mqtt/device_ai.model.dart';
import 'package:surgsmart/src/models/mqtt/interactive.model.dart';
import 'package:surgsmart/src/models/mqtt/rtc_live.model.dart';
import 'package:surgsmart/src/models/network_device_model.dart';
import 'package:surgsmart/src/modules/expand_screen/expand_screen/expand_screen.controller.dart';
import 'package:surgsmart/src/modules/report/surgery_report/surgery_report.controller.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/views/operation/tools/drawing_board_notifier.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/widgets/play_back.widget.dart';
import 'package:surgsmart/src/modules/surgery/surgery_room/widgets/voice_user_selector.widget.dart';
import 'package:surgsmart/src/routes/go_paths.dart';
import 'package:surgsmart/src/tools/app_context.dart';
import 'package:surgsmart/src/tools/app_preferences.dart';
import 'package:surgsmart/src/tools/app_voice.dart';
import 'package:surgsmart/src/tools/image_util.dart';
import 'package:surgsmart/src/tools/multi_window.dart';
import 'package:surgsmart/src/tools/throttle_control.dart';
import 'package:surgsmart/src/tools/device_cmd.dart';
import 'package:surgsmart/src/tools/event_bus.dart';
import 'package:surgsmart/src/tools/toast.dart';
import 'package:surgsmart/src/widgets/modal.widget.dart';
import 'package:surgsmart/src/widgets/smart_voice.widget.dart';
import 'package:tencent_cos_plus/tencent_cos_plus.dart';
import 'package:go_router/go_router.dart';

class RoomAndLiveStatus {
  final bool isRtcLiveOngoing;
  final bool isRtmpLiveOngoing;

  RoomAndLiveStatus({
    required this.isRtcLiveOngoing,
    required this.isRtmpLiveOngoing,
  });
}

/// 所属模块: surgery
///
/// 手术
class SurgeryRoomController extends AppController
    with StateMixin<SurgeryInfo>, VoiceHandler {
  SurgeryRoomController(super.key, super.routerState);

  late final Monitor monitorController;
  late final Recorder recordController;

  late final SurgeryInfo surgeryInfo;

  final GlobalKey<SurgPlaybackState> playbackKey = GlobalKey();

  ///扩展的第二路视频源控制器
  Monitor? monitor2Controller;

  RoomAuthorizeInfo? _roomAuthorizeInfo;
  RoomAuthorizeInfo? _roomAuthorizeInfo2;

  StartLiveInfo? startLiveInfo;
  //可邀请用户列表
  InvitationInfo? invitationInfo;

  //声网状态：声网仅在手术房间使用，故声网状态暂存于此，内部管理保证生命周期内有效性，
  //静态变量对外提供以作日志收集
  static RtcStats? agoraRtcStats;

  //出血数据
  BleedingData? _bleedingInfo;
  // 记录timer定时任务中封面上传时间
  int lastUpTime = 0;
  // 上次协同用户说话检查时间记录
  int lastSpeakCheckTime = 0;

  RtcLive? rtcLiveController;
  RtmpLive? rtmpLiveController;
  //扩展的第二路视频源协同/直播控制器
  RtcLive? rtcLive2Controller;
  RtmpLive? rtmpLive2Controller;
  // 手术画面采集帧率
  static const int framerate = 24;

  //手术源视频帧捕获控制器
  final videoScreenshotController = ScreenshotController();

  //回放视频大屏同步
  final playbackLargeScreenSync = false.notifier;

  //大屏是否可用
  final largeScreenEnable = false.notifier;

  final isShowSignalMessage = false.notifier;
  //控制开启关闭直播按钮展示状态
  final isLiveStarting = false.notifier;
  final isLiveStopping = false.notifier;

  // 是否开启教学录音
  final switchRecording = false.notifier;
  // 是否开启摄像头推流
  final switchCamera = false.notifier;
  // 控制摄像头画面显示
  final isShowExternalVideo = false.notifier;
  // 网络类型发生变化
  final networkTypeChange = false.notifier;

  /// 手术ID
  late final int surgeryId;

  /// 允许使用专科术式分析
  late final bool enableAdvanceAi;

  /// 会诊/直播
  final roomAndLiveStatus =
      RoomAndLiveStatus(
        isRtcLiveOngoing: false,
        isRtmpLiveOngoing: false,
      ).notifier;

  /// 麦克风开关
  final microphoneIsOn = false.notifier;

  /// 体腔外模糊开关
  final outsideBlurIsOn = false.notifier;

  /// 体腔外状态
  final isBodyout = false.notifier;

  final markImagePath = "".notifier;

  /// 回放控制
  final playbackControl = ValueNotifier<PlaybackInfo?>(null);
  final showSurgPlayback = false.notifier; //术中回放标志位

  /// 追踪显示
  final isTracking = false.notifier;

  /// 用户主动退出追踪
  var trackingExitByUser = false;

  /// 用户主动退出次数(-1 表示永久退出)
  int trackingExitCount = 0;

  /// 出血web端通知
  bool isBloodNotify = false;

  /// 标记当前是否已显示noSignal弹窗
  bool isShowNoSignalDialog = false;

  /// 标记是否首次信号恢复
  final firstSignalRecover = false.notifier;

  /// 协同医生
  final doctors = <DoctorInfo>[].notifier;

  /// 观看直播用户数量
  final liveUserCount = 0.emptyNotifier;

  final currentPhaseId = ValueNotifier<int?>(null);

  var liveUserRemainCounter = 0;

  final videoBitrate = 0.notifier;

  /// 白板绘图
  final graphics = <UserGraphic>[].notifier;

  /// 白板绘图过期检测等任务定时器
  Timer? taskTimer;

  /// 画面捕获任务
  //Timer? _captureTimer;

  /// 标记手术结束, 用于遥控器回复手术状态
  var _markSurgeryStop = false;

  /// 标记回放视频合并任务正在进行中
  bool playbackMerging = false;

  /// 标记语音控制是否准备完成
  bool voicePrepare = false;

  /// 标记media_kit是否正在截屏
  //bool screenshotNow = false;

  /// 记录上次回放合并时间
  int _lastMergeTime = 0;

  /// 当前设置的扬声器
  final currentLoudspeaker = ValueNotifier<DeviceSettingInfo?>(null);

  /// 大屏回放相关
  //final sharedBuffer = ffi.malloc<ffi.Uint8>(3840 * 2160 * 4);
  /// key：显示器名称，value：RenderersAddress
  /// 历史所有的RenderersAddress记录
  Map<String, dynamic> allTextureRenderersAddress = {};

  /// 当前显示的RenderersAddress
  Map<String, dynamic> textureRenderersAddress = {};

  // 直播视频采集缓存区
  final liveSharedBuffer = malloc<ffi.Uint8>(3840 * 2160 * 4);

  /// 当前设置的麦克风
  DeviceSettingInfo? currentMicrophone;

  /// 当前设置的采集摄像头
  DeviceSettingInfo? currentVideoDevice;

  ///已连接的网络
  List<NetworkDevice> connectedNetWorks = [];

  ///当前展示的麦克风列表
  List<DeviceSettingInfo> microphoneDevices = [];

  ///当前展示的扬声器列表
  List<DeviceSettingInfo> loudspeakerDevices = [];

  ///当前展示的视频采集器列表
  List<DeviceSettingInfo> videoDevices = [];

  ///按序排列生成的术中回放视频列表
  List<MergeVideoInfo> playbackList = [];

  MqttApiObserver? onCursorObserver;
  MqttApiObserver? onEmptyObserver;
  MqttApiObserver? onPointPathObserver;
  MqttApiObserver? onRedoObserver;
  MqttApiObserver? onUndoObserver;

  MqttApiObserver? onDoingObserver;

  MqttApiObserver? onSurgeryReadObserver;
  MqttApiObserver? onBleedingObserver;

  StreamSubscription? powerOffObserver;

  StreamSubscription? screenObserver;
  StreamSubscription? ethernetObserver;

  final maskIpc = MaskIpc("/mask_algorithm_data", 600, [
    0xFF4D4F99, // 红色
    0x42A1FF99, // 蓝色
    0x1AFF2B99, // 绿色
  ]);

  final modelPlayer = Player();

  late final modelVideoController = VideoController(modelPlayer);

  var _eventFlags = 0;

  late final List<Media> medias;

  @override
  void onInit() {
    /// 解析参数
    final extra = (routerState?.extra as Map<String, dynamic>);
    surgeryId = extra['surgeryId'] as int;
    enableAdvanceAi = extra['enableAdvanceAi'] ?? true;
    bool isNewSurgery = extra['isNewSurgery'] ?? true;
    if (isNewSurgery) {
      AppPreferences.noSignalAll.setBool(true);
    }

    modelPlayer.setPlaylistMode(PlaylistMode.none);
    modelPlayer.setVolume(0);
    final pathSegments = Platform.resolvedExecutable.split("/")..removeLast();
    pathSegments.addAll(["data", "flutter_assets", "packages", "surgsmart"]);
    medias =
        [
          R.text.asset.model_1_mp4_txt.assetName,
          R.text.asset.model_2_mp4_txt.assetName,
          R.text.asset.model_3_mp4_txt.assetName,
          R.text.asset.model_4_mp4_txt.assetName,
        ].map((assetName) {
          final path = (pathSegments + [assetName]).join("/");
          return Media(path);
        }).toList();
    modelPlayer.open(medias[0], play: false);

    maskIpc.onConnectionChanged = (connected) {
      app.logI("maskIpc connection changed: $connected");
    };
    maskIpc.onTrackingChanged = (isTracking) {
      if (!isTracking && trackingExitCount != -1) {
        trackingExitByUser = false;
      }
      this.isTracking.value = trackingExitByUser ? false : isTracking;
      if (isTracking) {
        maskIpc.onFrameDataChanged ??= (tissueIds, eventFlags) {
          sendTrackingEventsMessageToWindows(eventFlags);
        };
      } else {
        maskIpc.onFrameDataChanged = null;
        modelPlayer.open(medias[0], play: false);
        _eventFlags = 0;
        isStartShowModel = false;
        sendTrackingEventsMessageToWindows(_eventFlags);
      }
    };
    if (maskIpc.initialize()) {
      app.logI('MaskIpc初始化成功');
      maskIpc.start();
      app.logI('MaskIpc已启动数据轮询');
    } else {
      app.logE('MaskIpc初始化失败');
    }

    /// 创建本地媒体处理器
    createLocalMediaHandler();

    //采集任务
    //startCapture();

    /// 初始化定时器
    createTaskTimer();

    /// 音视频外设监听
    HostDevice.share.audioSources.addListener(audioSourcesChanged);
    HostDevice.share.audioSinks.addListener(audioSinksChanged);
    HostDevice.share.videoSources.addListener(videoDeviceChanged);
    HostDevice.share.startListenVideoCaptureDevices();
    HostDevice.share.startListenAudioDevices();

    /// 直播状态监听
    roomAndLiveStatus.addListener(liveOrDoctorsChanged);

    /// 协同列表监听
    doctors.addListener(liveOrDoctorsChanged);

    /// 直播观看用户监听
    liveUserCount.addListener(userCountOrAiPhaseChanged);

    /// 算法阶段变化监听
    currentPhaseId.addListener(userCountOrAiPhaseChanged);

    /// 视频信号监听
    recordController.hasSignal.addListener(videoSignalChanged);
    videoSignalChanged(); //首次同步信号状态
  }

  @override
  void onReady() async {
    /// 开始手术
    try {
      update(LoadState.success(await startSurgery()));
    } catch (error) {
      app.logE(error.toString());
      if (context != null) {
        Modal(
          kind: ModalKind.dialog,
          type: ModalType.error,
          closable: false,
          title: S.current.f_zzT87YnB,
          message: S.current.f_MtfuK21I,
          confirmText: S.current.f_MtRemwCS,
          onConfirm: () {
            context?.pop();
            onReady();
          },
        ).show(context!);
      }
      return;
    }

    /// 订阅远端互动 MQTT 消息
    subscriptRemoteInteractionMessage();

    /// 订阅设备网络状态
    subscriptNetworkStatus();
    AppContext.share.networkConnected.addListener(subscriptNetworkStatus);

    /// 订阅出血等业务消息
    subscriptLocationMqttMessage();

    /// 订阅关机等自定义事件
    subscriptRoomEventBus();
  }

  @override
  void onClose() {
    modelPlayer.dispose();
    maskIpc.dispose();
    VoiceHelper.share.removeHandler(this);
    VoiceHelper.share.disable();
    AppContext.share.networkConnected.removeListener(subscriptNetworkStatus);
    HostDevice.share.audioSources.removeListener(audioSourcesChanged);
    HostDevice.share.audioSinks.removeListener(audioSinksChanged);
    HostDevice.share.videoSources.removeListener(videoDeviceChanged);

    roomAndLiveStatus.removeListener(liveOrDoctorsChanged);
    doctors.removeListener(liveOrDoctorsChanged);
    recordController.hasSignal.removeListener(videoSignalChanged);
    sendSurgeryStatusToWindows();

    agoraRtcStats = null;

    onCursorObserver?.cancel();
    onEmptyObserver?.cancel();
    onPointPathObserver?.cancel();
    onRedoObserver?.cancel();
    onUndoObserver?.cancel();
    onBleedingObserver?.cancel();
    onDoingObserver?.cancel();

    onSurgeryReadObserver?.cancel();
    powerOffObserver?.cancel();
    screenObserver?.cancel();
    ethernetObserver?.cancel();

    isShowSignalMessage.dispose();
    graphics.dispose();
    taskTimer?.cancel();
    microphoneIsOn.dispose();
    outsideBlurIsOn.dispose();
    playbackControl.dispose();
    isBodyout.dispose();
    roomAndLiveStatus.dispose();
    doctors.dispose();
    liveUserCount.dispose();
    videoBitrate.dispose();
    isLiveStarting.dispose();
    isLiveStopping.dispose();
    showSurgPlayback.dispose();
    currentPhaseId.dispose();
    currentLoudspeaker.dispose();
    for (var address in textureRenderersAddress.values) {
      removeRender(address);
    }
    //malloc.free(sharedBuffer);
    malloc.free(liveSharedBuffer);
  }

  void removeRender(int address) {
    playbackKey.currentState?.getController()?.removeRender(render: address);
  }

  /// 创建本地媒体处理器
  void createLocalMediaHandler() {
    if (Platform.isLinux) {
      initMediasChoose();
      final videoCapture = getDefaultVideoCapture();
      if (videoCapture == null) return;
      if (AppPreferences.resolution.intValue == 2160) {
        monitorController = Monitor(
          videoCaptureDevice: videoCapture,
          width: 3840,
          height: 2160,
          framerate: 30,
        );

        recordController = Recorder(
          videoCaptureDevice: videoCapture,
          audioSourceName: getAudioSourceName(),
          spaceName: "$surgeryId",
          width: 3840,
          height: 2160,
          videoBitrate: 24000,
          framerate: 60,
          onMerged: onMergedVideo,
        );
      } else {
        monitorController = Monitor(
          videoCaptureDevice: videoCapture,
          framerate: 30,
        );
        recordController = Recorder(
          videoCaptureDevice: videoCapture,
          audioSourceName: getAudioSourceName(),
          spaceName: "$surgeryId",
          videoBitrate: 6000,
          framerate: 60,
          onMerged: onMergedVideo,
        );
      }

      Future.delayed(3.seconds).then((val) {
        setCameraSwitch(enabled: cameraState());
      });

      smartVoicePrepare();
    }
  }

  void initMediasChoose() {
    getLoudspeakerDevices();
    getMicrophoneDevices();
    getExtendedVideoDevices();
  }

  /// 创建远程媒体处理器
  Future<void> createRtcController() async {
    if (Platform.isLinux) {
      initMediasChoose();
      if (getDefaultVideoCapture() == null) return;
      final deviceId = AppContext.share.authorizeInfo.deviceId;
      _roomAuthorizeInfo ??=
          await HttpRoomApi<RoomAuthorizeInfo>.authorize(
            deviceId: deviceId,
          ).request();
      app.logW("声网参数：${_roomAuthorizeInfo.toString()}");
      createRtc();
    }
  }

  /// 创建第二路远程媒体处理器
  Future<void> createRtc2Controller() async {
    if (currentVideoDevice == null) return;
    if (Platform.isLinux) {
      final deviceId = AppContext.share.authorizeInfo.deviceId;
      _roomAuthorizeInfo2 ??=
          await HttpRoomApi<RoomAuthorizeInfo>.authorize(
            isEndoscope: false,
            deviceId: deviceId,
            randomUid: true,
          ).request();
      app.logW("声网参数2：${_roomAuthorizeInfo2.toString()}");
      AgoraInfo agora = _roomAuthorizeInfo2!.agora;

      /// 摄像头视频源协同/直播控制器
      rtcLive2Controller?.dispose();
      rtcLive2Controller = RtcLive(
        videoCaptureDevice: currentVideoDevice!.info,
        audioSourceName: getAudioSourceName(),
        audioSinkName: getAudioSinkName(),
        streamType: StreamType.secondary,
        appId: agora.appId,
        token: agora.token,
        channelId: agora.channelName,
        userId: agora.uid.toString(),
        onUserJoined: (userId) {
          app.logD("videoInfo: rtc2 user joined: $userId");
        },
        onUserLeft: (userId, reason) {
          app.logD("videoInfo: rtc2 user left: $userId, reason: $reason");
        },
        onTransportStats: (rtcStats) {
          app.logD(
            "videoInfo: rtc2 transport stats: ${rtcStats.sendVideoBitrate}kbps",
          );
        },
        onReInit: () async {
          rtcLive2Controller?.dispose();
          if (currentVideoDevice != null) {
            await createRtc2Controller();
            rtcLive2Controller?.init();
            if (doctors.value.isEmpty) {
              rtcLive2Controller?.stop();
            }
          }
        },
      );
    }
  }

  Future<void> createRtmp2Controller() async {
    final deviceId = AppContext.share.authorizeInfo.deviceId;
    _roomAuthorizeInfo2 ??=
        await HttpRoomApi<RoomAuthorizeInfo>.authorize(
          deviceId: deviceId,
          isEndoscope: false,
          randomUid: true,
        ).request();
    rtmpLive2Controller?.dispose();

    rtmpLive2Controller = RtmpLive(
      videoCaptureDevice: currentVideoDevice!.info,
      audioSourceName: getAudioSourceName(),
      streamType: StreamType.secondary,
      appId: _roomAuthorizeInfo2!.agora.appId,
      url: startLiveInfo!.externalPushUrl,
      framerate: framerate,
    );
  }

  void notifyTaskState() {
    eventBus.fire(EventBusInfo(type: EventBusType.taskStateChange));
  }

  void refreshHandler() {
    //限制刷新帧率最高约55帧，自测单个协同绘制最快刷新间隔18ms,不阻碍单个绘制流畅度，此处为多协同绘制流畅度考虑
    if (Throttle().checkPass("graphicsRefresh", intervalMs: 18)) {
      graphics.value = [...graphics.value];
    }
  }

  /// 订阅远端互动 MQTT 消息
  void subscriptRemoteInteractionMessage() {
    onCursorObserver = MqttWhiteboardApi<Graphic>.onCursorMessage(
      surgeryId: surgeryId,
    ).listen((message) {
      sendInteractionMessageToWindows('drawCursor', message);

      DrawingBoardNotifier.drawCursor(
        getOrCreateGraphic(message.userId),
        message,
      );
      refreshHandler();
    });
    onEmptyObserver = MqttWhiteboardApi<GraphicSet>.onEmptyMessage(
      surgeryId: surgeryId,
    ).listen((message) {
      sendInteractionMessageToWindows('clearGraphics', message);

      DrawingBoardNotifier.clearGraphics(
        getOrCreateGraphic(message.userId),
        uuids: message.uuids,
      );
      refreshHandler();
    });

    onPointPathObserver = MqttWhiteboardApi<Graphic>.onPointPathMessage(
      surgeryId: surgeryId,
    ).listen((message) {
      sendInteractionMessageToWindows('drawGraphic', message);
      DrawingBoardNotifier.drawGraphic(
        getOrCreateGraphic(message.userId),
        message,
        message,
      );
      refreshHandler();
    });

    onRedoObserver = MqttWhiteboardApi<GraphicSet>.onRedoMessage(
      surgeryId: surgeryId,
    ).listen((message) {
      sendInteractionMessageToWindows('redo', message);
      DrawingBoardNotifier.redo(
        getOrCreateGraphic(message.userId),
        message.uuids,
      );
      refreshHandler();
    });

    onUndoObserver = MqttWhiteboardApi<GraphicSet>.onUndoMessage(
      surgeryId: surgeryId,
    ).listen((message) {
      sendInteractionMessageToWindows('undo', message);
      DrawingBoardNotifier.undo(
        getOrCreateGraphic(message.userId),
        message.uuids,
      );
      refreshHandler();
    });

    onDoingObserver = MqttTrackApi<Graphic>.onDoingMessage(
      surgeryId: surgeryId,
    ).listen((message) {
      sendInteractionMessageToWindows('onDoing', message);
      DrawingBoardNotifier.drawGraphic(getOrCreateGraphic(0), message, null);
      refreshHandler();
    });

    onSurgeryReadObserver = MqttDeviceControlApi.onSurgeryReadRequest().listen((
      message,
    ) {
      broadcastSettingStatus(
        ClientMsgType.surgeryStatus,
        surgeryId: _markSurgeryStop ? null : surgeryId,
        isLiveOngoing:
            _markSurgeryStop
                ? false
                : roomAndLiveStatus.value.isRtmpLiveOngoing,
      );
    });
  }

  /// 订阅设备网络状态
  void subscriptNetworkStatus() async {
    if (AppContext.share.networkConnected.value) {
      onCursorObserver?.restore();
      onEmptyObserver?.restore();
      onPointPathObserver?.restore();
      onRedoObserver?.restore();
      onUndoObserver?.restore();
      onDoingObserver?.restore();
      onSurgeryReadObserver?.restore();

      HttpSurgeryApi.ping(surgeryId: surgeryId).request();

      if (_roomAuthorizeInfo == null) {
        /// 创建远端媒体处理器
        await createRtcController();
      }
      if (switchCamera.value) {
        /// 创建第二路远端媒体处理器
        await createRtc2Controller();
      }
      restoreOnlineServer();
    } else {
      onCursorObserver?.cancel();
      onEmptyObserver?.cancel();
      onPointPathObserver?.cancel();
      onRedoObserver?.cancel();
      onUndoObserver?.cancel();
      onDoingObserver?.cancel();
      onSurgeryReadObserver?.cancel();

      if (roomAndLiveStatus.value.isRtcLiveOngoing) {
        rtcLiveController?.dispose();
        rtcLive2Controller?.dispose();
      }
      if (roomAndLiveStatus.value.isRtmpLiveOngoing) {
        rtmpLiveController?.dispose();
        rtmpLive2Controller?.dispose();
      }

      isShowSignalMessage.value = true;
      Future.delayed(3.seconds).then((value) {
        isShowSignalMessage.value = false;
      });
    }
  }

  /// 恢复在线服务
  void restoreOnlineServer() {
    if (roomAndLiveStatus.value.isRtcLiveOngoing) {
      rtcLiveController?.dispose();
      createRtc();
      rtcLiveController?.init();
      attachAudioHandler();

      if (doctors.value.isEmpty) {
        rtcLiveController?.stop();
      }
      rtcLiveController?.speak(microphoneIsOn.value);

      if (switchCamera.value) {
        rtcLive2Controller?.dispose();
        rtcLive2Controller?.init();
        if (doctors.value.isEmpty) {
          rtcLive2Controller?.stop();
        }
        rtcLive2Controller?.speak(microphoneIsOn.value);
      }
    } else {
      startRoom();
    }

    if (roomAndLiveStatus.value.isRtmpLiveOngoing) {
      rtmpLiveController?.dispose();
      rtmpLiveController?.init();
      if (switchCamera.value) {
        rtmpLive2Controller?.init();
      }
      rtmpLiveController?.speak(microphoneIsOn.value);
      rtmpLive2Controller?.speak(microphoneIsOn.value);
    } else {
      if (AppPreferences.lastLiveTitle.stringValue?.isNotEmpty == true) {
        startLive(title: AppPreferences.lastLiveTitle.stringValue);
      }
    }
  }

  Future<SurgeryInfo> startSurgery() async {
    surgeryInfo =
        await HttpSurgeryApi<SurgeryInfo>.info(surgeryId: surgeryId).request();

    await HttpSurgeryApi.start(surgeryId: surgeryId).request();

    app.logW("surgeryInfo: ${surgeryInfo.toMap()}");
    await HttpLocalApi.startAi(
      surgeryId: surgeryId,
      algorithmCode: surgeryInfo.procedure?.algorithmCode ?? "COMMON",
      videoDevicePath: await DeviceCmd.share.videoPathInfo(),
      procedureCode: surgeryInfo.procedure?.procedureConfig?.procedureCode,
    ).request();

    if (Platform.isLinux) {
      if (!monitorController.init()) {
        throw "监视控制器初始化失败!";
      }

      if (!recordController.init()) {
        throw "录制控制器初始化失败!";
      }
    }

    broadcastSettingStatus(
      ClientMsgType.surgeryStatus,
      surgeryId: surgeryId,
      isLiveOngoing: roomAndLiveStatus.value.isRtmpLiveOngoing,
    );

    publishActiveStatus();

    return surgeryInfo;
  }

  String? loadingStatus;
  Future<void> stopSurgery({bool shutDown = false}) async {
    await modelPlayer.dispose();
    await modelVideoController.clearRenders();
    attachAudioHandler(audioRecording: false, isRtmpLiveOngoing: false);

    loadingStatus = shutDown ? S.current.f_MUgW83By : S.current.f_MUwJTaNw;
    update(LoadState.loading());

    try {
      if (Platform.isLinux) {
        monitorController.dispose();
        recordController.dispose();
        await closeCamera();
        if (_roomAuthorizeInfo != null) {
          rtcLiveController?.dispose();
          rtmpLiveController?.dispose();
        }
      }

      final surgery = state.value;
      await HttpLocalApi.stopAi(
        surgeryId: surgeryId,
        procedureCode: surgery?.procedure?.algorithmCode ?? "COMMON",
      ).request();
      app.logI('stopAi-----------success');

      if (roomAndLiveStatus.value.isRtmpLiveOngoing) {
        await stopLive(isStopSurgery: true);
        app.logI('stopLive-----------success');
      }

      if (roomAndLiveStatus.value.isRtcLiveOngoing &&
          AppContext.share.networkConnected.value) {
        await HttpRoomApi.stop(surgeryId: surgeryId).request();
      }

      roomAndLiveStatus.value = RoomAndLiveStatus(
        isRtcLiveOngoing: false,
        isRtmpLiveOngoing: false,
      );

      await HttpSurgeryApi.stop(surgeryId: surgeryId).request();

      _markSurgeryStop = true;
      currentPhaseId.value = null;

      broadcastSettingStatus(
        ClientMsgType.surgeryStatus,
        surgeryId: null,
        isLiveOngoing: false,
      );

      publishActiveStatus(retain: false);

      if (shutDown) {
        DeviceCmd.share.shutdownDevice();
        return;
      }

      app.openInner(
        GoPaths.surgeryReport,
        arguments: ReportArguments(
          surgeryId: surgeryId,
          surgeryInfo: surgeryInfo,
        ),
        isReplaceCurrent: true,
      );
    } catch (error) {
      app.logE(error.toString());
      if (context != null) {
        Modal(
          kind: ModalKind.dialog,
          type: ModalType.error,
          closable: false,
          title: S.current.f_zzT87YnB,
          message: S.current.f_MUwJGgqN,
          confirmText: S.current.f_MtRemwCS,
          onConfirm: () {
            context?.pop();
            stopSurgery(shutDown: shutDown);
          },
        ).show(context!);
      }
    }
  }

  void startLive({SurgeryInfo? surgery, String? title}) {
    if (isLiveStarting.value || getDefaultVideoCapture() == null) return;
    isLiveStarting.value = true;
    surgery = surgery ?? state.value!;
    title =
        title ??
        "${surgery.procedure?.name}-${surgery.user?.name}-${S.current.f_8PBHyiTS}";
    HttpLiveApi<StartLiveInfo>.start(surgeryId: surgeryId, title: title)
        .request()
        .then((data) async {
          app.logW("直播地址：${data.toString()}");
          startLiveInfo = data;
          final deviceId = AppContext.share.authorizeInfo.deviceId;
          _roomAuthorizeInfo ??=
              await HttpRoomApi<RoomAuthorizeInfo>.authorize(
                deviceId: deviceId,
              ).request();
          rtmpLiveController = RtmpLive(
            videoCaptureDevice: getDefaultVideoCapture()!,
            streamType: StreamType.main,
            audioSourceName: getAudioSourceName(),
            appId: _roomAuthorizeInfo!.agora.appId,
            url: data.pushUrl,
            videoHandlerPluginId: MediasKit.pluginId,
            onVideoHandler: (data) {
              final pixels = data.value.asTypedList(data.len);
              ui.decodeImageFromPixels(
                pixels,
                data.stride,
                data.height,
                ui.PixelFormat.rgba8888,
                (image) async {
                  ///直播中
                  pushLiveVideoFrame(image);
                },
              );
            },
          );
          if (rtmpLiveController?.init() != true) {
            throw "直播控制器初始化失败!";
          }

          if (switchCamera.value && currentVideoDevice != null) {
            await createRtmp2Controller();
            if (rtmpLive2Controller?.init() != true) {
              throw "直播控制器2初始化失败!";
            }
            rtmpLive2Controller?.speak(microphoneIsOn.value);
            rtmpLive2Controller?.changeAudioSource(
              name: currentMicrophone?.info ?? "default",
            );
          }
          return;
        })
        .then((_) {
          liveUserCount.value = 0;

          rtmpLiveController?.speak(microphoneIsOn.value);

          rtmpLiveController?.changeAudioSource(
            name: currentMicrophone?.info ?? "default",
          );

          AppPreferences.lastLiveTitle.setString(title!);

          roomAndLiveStatus.value = RoomAndLiveStatus(
            isRtcLiveOngoing:
                roomAndLiveStatus.value.isRtcLiveOngoing, //这里修改为保留rtc状态
            isRtmpLiveOngoing: true,
          );
          //推送rtc音频
          attachAudioHandler();

          isLiveStarting.value = false;

          /// 通知遥控器手术状态: 直播中
          final controlToken = AppContext.share.controlToken.last;
          if (controlToken?.isNotEmpty == true) {
            broadcastSettingStatus(
              ClientMsgType.surgeryStatus,
              surgeryId: surgeryId,
              isLiveOngoing: true,
            );
          }
          //通知直播shareKey获取
          eventBus.fire(EventBusInfo(type: EventBusType.surgeryLiveState));

          ToastUtils.showToast(
            context!,
            message: S.current.f_9aUbGUbm,
            type: ToastType.success,
          );
        })
        .onError((error, stackTrace) {
          isLiveStarting.value = false;

          ToastUtils.showToast(
            context!,
            message: S.current.f_Pjd2tDZJ,
            type: ToastType.error,
          );
        });
  }

  Future<void> stopLive({bool isStopSurgery = false}) async {
    try {
      if (AppContext.share.networkConnected.value) {
        await HttpLiveApi.stop(surgeryId: surgeryId).request();
      }
      if (!isStopSurgery) {
        attachAudioHandler(isRtmpLiveOngoing: false);
      }
      rtmpLiveController?.dispose();
      rtmpLive2Controller?.dispose();
      isLiveStopping.value = true;
      liveUserCount.value = null;
      startLiveInfo = null;
      AppPreferences.lastLiveTitle.remove();
      roomAndLiveStatus.value = RoomAndLiveStatus(
        isRtcLiveOngoing: roomAndLiveStatus.value.isRtcLiveOngoing,
        isRtmpLiveOngoing: false,
      );
      isLiveStopping.value = false;

      /// 通知遥控器手术状态: 未直播
      final controlToken = AppContext.share.controlToken.last;
      if (controlToken?.isNotEmpty == true) {
        broadcastSettingStatus(
          ClientMsgType.surgeryStatus,
          surgeryId: _markSurgeryStop ? null : surgeryId,
          isLiveOngoing: false,
        );
      }
      if (!isStopSurgery) {
        ToastUtils.showToast(
          context!,
          message: S.current.f_9aUbGUbm,
          type: ToastType.success,
        );
      }
    } catch (error) {
      ToastUtils.showToast(
        context!,
        message: S.current.f_Pjd2tDZJ,
        type: ToastType.error,
      );
      rethrow;
    }
  }

  void liveOrDoctorsChanged() {
    if (_markSurgeryStop) {
      AppContext.share.taskState = TaskState.nothing;
      notifyTaskState();
      return;
    }

    userCountOrAiPhaseChanged();
    if (doctors.value.isEmpty && !roomAndLiveStatus.value.isRtmpLiveOngoing) {
      AppContext.share.taskState = TaskState.nothing;
    } else {
      AppContext.share.taskState = TaskState.synergyOrLive;
    }
    notifyTaskState();

    //RtcLiveController初始化检测
    if (_roomAuthorizeInfo != null && Platform.isLinux) {
      if (doctors.value.isNotEmpty) {
        rtcLiveController?.start();
        if (switchCamera.value) {
          rtcLive2Controller?.start();
        }
      } else {
        rtcLiveController?.stop();
        rtcLive2Controller?.stop();
      }
    }
  }

  ///是否全程无视频输入
  bool noSignalAll() {
    return AppPreferences.noSignalAll.boolValue ?? true;
  }

  void videoSignalChanged() {
    // todo 取消视频录制影响视频信号检测，这里暂固定返回调试模拟回放视频
    bool tempNoSignalAll = noSignalAll();

    if (recordController.hasSignal.value) {
      AppPreferences.noSignalAll.setBool(false);
      if (tempNoSignalAll) {
        // 首次信号恢复
        firstSignalRecover.value = true;
      }
      // 信号状态改变暂以协同直播状态触发刷新
      roomAndLiveStatus.value = RoomAndLiveStatus(
        isRtcLiveOngoing: roomAndLiveStatus.value.isRtcLiveOngoing,
        isRtmpLiveOngoing: roomAndLiveStatus.value.isRtmpLiveOngoing,
      );
    }
    app.logW("信号改变------------noSignalAll-${noSignalAll()}");
    eventBus.fire(
      EventBusInfo<bool>(
        type: EventBusType.videoSignalChange,
        data: recordController.hasSignal.value,
      ),
    );
  }

  void userCountOrAiPhaseChanged({
    bool retain = true,
    bool isThumbnailUpdated = false,
  }) {
    publishActiveStatus(
      consultationUserCount: doctors.value.length,
      liveUserCount:
          roomAndLiveStatus.value.isRtmpLiveOngoing
              ? liveUserCount.value
              : null,
      phaseId: currentPhaseId.value,
      isThumbnailUpdated: isThumbnailUpdated,
      retain: retain,
    );
  }

  void publishActiveStatus({
    int consultationUserCount = 0,
    int? liveUserCount,
    int? phaseId,
    bool isThumbnailUpdated = false,
    bool retain = true,
  }) {
    MqttDeviceStatusApi.publishActiveStatus(
      deviceId: AppContext.share.authorizeInfo.deviceId,
      liveUserCount: liveUserCount,
      consultationUserCount: consultationUserCount,
      phaseId: phaseId,
      isThumbnailUpdated: isThumbnailUpdated,
    ).publish(retain: retain);
  }

  Future<void> startRoom() async {
    HttpRoomApi.start(surgeryId: surgeryId)
        .request()
        .then((_) {
          if (rtcLiveController?.init() == true) {
            if (doctors.value.isEmpty) {
              rtcLiveController?.stop();
            }
            attachAudioHandler();

            return;
          }
          throw "rtcLiveControllerInit: 开启协同失败!";
        })
        .then((_) {
          app.logW("start room success----------");

          rtcLiveController?.speak(microphoneIsOn.value);
          roomAndLiveStatus.value = RoomAndLiveStatus(
            isRtcLiveOngoing: true,
            isRtmpLiveOngoing: roomAndLiveStatus.value.isRtmpLiveOngoing,
          );
          //通知协同key获取
          eventBus.fire(EventBusInfo(type: EventBusType.surgeryRoomState));
        })
        .onError((error, stackTrace) {
          app.logE("start room failed $error");

          if (!error.toString().contains('rtcLiveControllerInit')) {
            Future.delayed(5.seconds).then((_) {
              startRoom();
            });
          }
        });
  }

  Future<void> markTime() async {
    if (noSignalAll()) {
      ToastUtils.showToast(
        context!,
        message: S.current.f_O7KD7TEj,
        type: ToastType.info,
      );
      return;
    }
    if (!Throttle().checkPass("markTime", intervalMs: 1200)) {
      return;
    }
    ToastUtils.showToast(
      context!,
      message: S.current.f_MBmazWZZ,
      type: ToastType.info,
    );
    final surgery = state.value!;
    final directory = (await getApplicationDocumentsDirectory()).path;
    final savedDir = "$directory/v202310/${surgery.id}/screenshots";
    Directory(savedDir).createSync(recursive: true);

    final beginTime = recordController.firstFrameTimestampWithOffset();
    final timePoint =
        (DateTime.now().millisecondsSinceEpoch - beginTime) ~/ 1000;
    String fileName = '$timePoint.png';
    final imagePath = await videoScreenshotController.captureAndSave(
      savedDir,
      fileName: fileName,
    );
    playAudio(fileName: "mark_screenshot.wav.txt");
    String message = S.current.f_9av41fDT;

    if (imagePath?.isNotEmpty == true) {
      //frameCount为0容错
      markImagePath.value =
          markImagePath.value == imagePath ? "file://$imagePath" : imagePath!;
      try {
        SurgeryMarkTimeInfo markTimeInfo =
            await HttpSurgeryApi<SurgeryMarkTimeInfo>.markTime(
              surgeryId: surgery.id,
              timePoint: timePoint,
              imagePath: imagePath!,
            ).request();
        message =
            markTimeInfo.id != 0 ? S.current.f_9av41fDT : S.current.f_Pjd2tDZJ;
      } catch (e) {
        message = S.current.f_Pjd2tDZJ;
      }
    } else {
      message = S.current.f_Pjd2tDZJ;
    }
    ToastUtils.showToast(context!, message: message, type: ToastType.success);
  }

  Future<void> surgPlayback() async {
    if (!Throttle.instance.checkPass("surgPlayback", intervalMs: 2000)) {
      return;
    }

    if (!Throttle.instance.checkPass("playbackExit", intervalMs: 2000)) {
      showSurgPlayback.value = true;
      Future.delayed(2.seconds).then((_) async {
        showSurgPlayback.value = await checkPlaybackVideoList(immediate: true);
      });
      return;
    }

    showSurgPlayback.value = await checkPlaybackVideoList(immediate: true);
  }

  //通知相关状态更新
  void broadcastSettingStatus(
    ClientMsgType type, {
    bool? enabled,
    int? surgeryId,
    bool? isLiveOngoing,
  }) {
    if (AppContext.share.enableBleControl) {
      // switch (type) {
      //   case ClientMsgType.microphoneState:
      //     PeripheralController.instance.notify(
      //       msgType: type.index,
      //       data: jsonEncode({"enabled": enabled}),
      //     );
      //     break;
      //   case ClientMsgType.bodyoutMask:
      //     PeripheralController.instance.notify(
      //       msgType: type.index,
      //       data: jsonEncode({"enabled": enabled}),
      //     );
      //     break;
      //   case ClientMsgType.surgeryStatus:
      //     PeripheralController.instance.notify(
      //       msgType: type.index,
      //       data: jsonEncode({
      //         "surgeryId": surgeryId,
      //         "isLiveOngoing": isLiveOngoing,
      //       }),
      //     );
      //     break;
      //   default:
      //     break;
      // }
    } else {
      // 此通知和遥控器无关，独立处理
      if (ClientMsgType.surgeryStatus == type) {
        MqttDeviceStatusApi.publishSurgeryStatus(
          deviceId: AppContext.share.authorizeInfo.deviceId,
          surgeryId: surgeryId,
          isLiveOngoing: isLiveOngoing!,
          isExternalCameraOn: switchCamera.value,
        ).publish(retain: _markSurgeryStop ? false : true);
      }
      if (AppContext.share.controlToken.last?.isNotEmpty != true) {
        return;
      }
      switch (type) {
        case ClientMsgType.microphoneState:
          MqttDeviceControlApi.broadcastMicrophoneSettingStatus(
            controlToken: AppContext.share.controlToken.last!,
            enabled: enabled!,
          ).publish();
          break;
        case ClientMsgType.bodyoutMask:
          MqttDeviceControlApi.broadcastBodyoutMaskSettingStatus(
            controlToken: AppContext.share.controlToken.last!,
            enabled: enabled!,
          ).publish();
          break;
        case ClientMsgType.surgeryStatus:
          MqttDeviceControlApi.broadcastSurgeryStatus(
            controlToken: AppContext.share.controlToken.last!,
            surgeryId: surgeryId,
            isLiveOngoing: isLiveOngoing!,
            isExternalCameraOn: switchCamera.value,
          ).publish();
          app.logW(
            'surgeryStatus： isLiveOngoing: $isLiveOngoing, isExternalCameraOn: $switchCamera， surgeryId: $surgeryId',
          );
          break;
        default:
          break;
      }
    }
  }

  bool cameraState() {
    return AppPreferences.cameraState.boolValue ?? false;
  }

  /// 切换摄像头
  Future<void> setVideoDevice(DeviceSettingInfo device) async {
    if (!switchCamera.value) {
      currentVideoDevice = device;
      return;
    }

    bool showExternalVideo = isShowExternalVideo.value;
    await setCameraSwitch(enabled: false);
    currentVideoDevice = device;
    await setCameraSwitch(enabled: true);
    if (showExternalVideo) {
      setExternalVideo(show: true);
    }
  }

  /// -------功能控制区start-------

  Future<void> setRecordAudioSwitch({required bool enabled}) async {
    if (enabled) {
      Modal(
        title: S.current.f_zbuzCjC0,
        kind: ModalKind.dialog,
        type: ModalType.info,
        confirmText: S.current.f_9KZD5TVS,
        cancelText: S.current.f_9KZD45AL,
        closable: false,
        message: S.current.f_MBEgdJTS,
        onConfirm: () {
          if (noSignalAll()) {
            ToastUtils.showToast(
              context!,
              message: S.current.f_MBYsiqzA,
              type: ToastType.info,
            );
          }
          switchRecording.value = enabled;
          app.context.pop();
          recordController.recordAudio(enabled);
        },
        onCancel: () {
          switchRecording.value = false;
          app.context.pop();
        },
      ).show(app.context);
      return;
    }
    switchRecording.value = false;
    recordController.recordAudio(enabled);
  }

  Future<void> setCameraSwitch({required bool enabled}) async {
    await AppPreferences.cameraState.setBool(enabled);
    switchCamera.value = enabled;
    if (enabled) {
      //开启摄像头再次校验当前摄像头是否存在
      getExtendedVideoDevices();
      if (currentVideoDevice == null) {
        ToastUtils.showToast(context!, message: S.current.f_MEhRr75X);
        return;
      }
      disposeMediaHandler2();
      monitor2Controller = Monitor(
        videoCaptureDevice: currentVideoDevice!.info,
        width: 1920,
        height: 1080,
        framerate: 30,
      );

      if (monitor2Controller?.init() != true) {
        throw "监视控制器初始化失败!";
      }

      if (!AppContext.share.networkConnected.value) {
        return;
      }

      /// 创建第二路远端媒体处理器
      await createRtc2Controller();

      if (rtcLive2Controller?.init() != true) {
        throw "协同控制器初始化失败!";
      }
      rtcLive2Controller?.speak(microphoneIsOn.value);

      if (roomAndLiveStatus.value.isRtmpLiveOngoing) {
        await createRtmp2Controller();
        if (rtmpLive2Controller?.init() != true) {
          throw "直播控制器初始化失败!";
        }
      }
      if (AppPreferences.showCameraVideo.boolValue == true) {
        setExternalVideo(show: true);
      }
    } else {
      await closeCamera();
    }

    /// 通知web摄像头直播状态
    broadcastSettingStatus(
      ClientMsgType.surgeryStatus,
      surgeryId: surgeryId,
      isLiveOngoing: roomAndLiveStatus.value.isRtmpLiveOngoing,
    );
  }

  /// 关闭摄像头UI/控制器
  Future<void> closeCamera() async {
    await setExternalVideo(show: false);
    await AppPreferences.cameraState.setBool(false);
    disposeMediaHandler2();
  }

  /// 释放第二路媒体控制器
  void disposeMediaHandler2() {
    monitor2Controller?.dispose();
    monitor2Controller = null;
    rtcLive2Controller?.dispose();
    rtmpLive2Controller?.dispose();
  }

  Future<void> setExternalVideo({required bool show}) async {
    AppPreferences.showCameraVideo.setBool(show);
    isShowExternalVideo.value = show;
  }

  void setBodyoutMaskState({required bool enabled}) {
    outsideBlurIsOn.value = enabled;

    broadcastSettingStatus(ClientMsgType.bodyoutMask, enabled: enabled);

    HttpSurgeryApi.setOutBlur(surgeryId: surgeryId, status: enabled).request();

    ToastUtils.showToast(
      context!,
      message: enabled ? S.current.f_zLzbimZm : S.current.f_zNFt01pz,
      type: ToastType.success,
    );
  }

  List<DeviceSettingInfo> getMicrophoneDevices() {
    microphoneDevices.clear();
    for (var audio in HostDevice.share.audioSources.value) {
      //修复插入外接设备情况下，设置非默认设备音量，读取到的音量依然为最大音量
      if (currentMicrophone?.info == audio.name.dartString) {
        audio.volume = currentMicrophone!.value;
      }
      if (audio.description.dartString.startsWith("SmartIC Audio Device") ||
          audio.description.dartString.startsWith("FY-SP003")) {
        microphoneDevices.insert(
          0,
          DeviceSettingInfo().initWith({
            "device": "系统默认（M3麦克风）",
            "value": audio.volume > 100 ? 100 : audio.volume,
            "info": audio.name.dartString,
          }),
        );
      } else if (!audio.description.dartString.startsWith(
        "PRO CAPTURE AIO 4K PLUS",
      )) {
        microphoneDevices.add(
          DeviceSettingInfo().initWith({
            "device": audio.description.dartString,
            "value": audio.volume > 100 ? 100 : audio.volume,
            "info": audio.name.dartString,
          }),
        );
      }
    }
    if (microphoneDevices.isNotEmpty) {
      int index = 0;
      String? microphoneSettings =
          AppPreferences.microphoneSettings.stringValue;
      if (microphoneSettings != null) {
        Map<String, dynamic> micSetting = jsonDecode(microphoneSettings);
        DeviceSettingInfo micSettingInfo = DeviceSettingInfo().initWith(
          micSetting,
        );
        index = microphoneDevices.indexWhere(
          (device) => device.info == micSettingInfo.info,
        );
      }
      index = index == -1 ? 0 : index;
      microphoneDevices[index].selected = true;
      if (currentMicrophone == null ||
          currentMicrophone?.info != microphoneDevices[index].info) {
        setMicrophoneConfig(microphoneDevices[index]);
      }
    }
    return microphoneDevices;
  }

  List<DeviceSettingInfo> getLoudspeakerDevices() {
    loudspeakerDevices.clear();
    for (var audio in HostDevice.share.audioSinks.value) {
      if (currentLoudspeaker.value?.info == audio.name.dartString) {
        audio.volume = currentLoudspeaker.value!.value;
      }
      if (audio.description.dartString.startsWith("内置音频") ||
          audio.description.dartString.startsWith("Built-in Audio")) {
        loudspeakerDevices.insert(
          0,
          DeviceSettingInfo().initWith({
            "device": "系统默认（M3扬声器）",
            "value": audio.volume > 100 ? 100 : audio.volume,
            "info": audio.name.dartString,
          }),
        );
      } else {
        loudspeakerDevices.add(
          DeviceSettingInfo().initWith({
            "device": audio.description.dartString,
            "value": audio.volume > 100 ? 100 : audio.volume,
            "info": audio.name.dartString,
          }),
        );
      }
    }

    if (loudspeakerDevices.isNotEmpty) {
      int index = 0;
      String? loudspeakerSettings =
          AppPreferences.loudspeakerSettings.stringValue;
      if (loudspeakerSettings != null) {
        Map<String, dynamic> loudSetting = jsonDecode(loudspeakerSettings);
        DeviceSettingInfo loudSettingInfo = DeviceSettingInfo().initWith(
          loudSetting,
        );
        index = loudspeakerDevices.indexWhere(
          (device) => device.info == loudSettingInfo.info,
        );
      }
      index = index == -1 ? 0 : index;
      loudspeakerDevices[index].selected = true;
      if (currentLoudspeaker.value == null ||
          currentLoudspeaker.value?.info != loudspeakerDevices[index].info) {
        Future.delayed(Duration(seconds: 2)).then((_) {
          setLoudspeakerConfig(loudspeakerDevices[index]);
        });
      }
    }
    return loudspeakerDevices;
  }

  /** 取消提示音量需求 */
  // double getTipVolume(String deviceName) {
  //   double tipVolume = 39321;
  //   String? deviceTipVolumeJson = AppPreferences.deviceTipVolume.stringValue;
  //   if (deviceTipVolumeJson != null) {
  //     Map<String, dynamic> deviceTipVolume = jsonDecode(deviceTipVolumeJson);
  //     tipVolume = deviceTipVolume[deviceName] ?? 39321;
  //   }
  //   return tipVolume;
  // }

  // Future<void> saveTipVolume(String deviceName, double value) async {
  //   String? deviceTipVolumeJson = AppPreferences.deviceTipVolume.stringValue;
  //   Map<String, dynamic> deviceTipVolume =
  //       deviceTipVolumeJson != null ? jsonDecode(deviceTipVolumeJson) : {};
  //   deviceTipVolume[deviceName] = value;
  //   AppPreferences.deviceTipVolume.setString(jsonEncode(deviceTipVolume));

  //   smartVoicePrepare();
  // }

  ///获取视频输入源列表
  List<DeviceSettingInfo> getExtendedVideoDevices() {
    videoDevices.clear();
    bool selected = false;
    for (var video in HostDevice.share.videoSources.value) {
      if (video.name.dartString.startsWith('13876BBF')) continue;
      if (video.isUsbExtend) {
        selected = currentVideoDevice?.device == video.name.dartString;
        videoDevices.add(
          DeviceSettingInfo().initWith({
            "device": video.name.dartString,
            "value": video.index,
            "info": video,
            "selected": selected,
          }),
        );
      }
    }
    if (videoDevices.isNotEmpty) {
      if (!selected) {
        videoDevices.first.selected = true;
        currentVideoDevice = videoDevices.first;
      } else {
        currentVideoDevice = videoDevices.firstWhere(
          (device) => device.selected,
        );
      }
    } else if (switchCamera.value) {
      currentVideoDevice = null;
      setCameraSwitch(enabled: false);
    }
    return videoDevices;
  }

  ffi.VideoCaptureDevice? getDefaultVideoCapture() {
    for (var video in HostDevice.share.videoSources.value) {
      if (!video.isUsbExtend) return video;
    }
    return null;
  }

  ///获取业务可用网络列表
  Future<List<DeviceSettingInfo>> getAvailableNetworkDevices() async {
    List<DeviceSettingInfo> networkDevices = [];
    Features features = AppContext.share.authorizeInfo.features;
    String? selectedNetwork = AppPreferences.selectedNetwork.stringValue;

    if (features.networkWired) {
      networkDevices.add(
        DeviceSettingInfo().initWith({
          "device": "有线网络",
          "value": NetWorkType.ethernet,
          "selected": selectedNetwork == NetWorkType.ethernet.name,
        }),
      );
    }
    if (features.networkWifi) {
      networkDevices.add(
        DeviceSettingInfo().initWith({
          "device": "Wi-Fi",
          "value": NetWorkType.wifi,
          "selected": selectedNetwork == NetWorkType.wifi.name,
        }),
      );
    }
    if (features.networkCellular) {
      networkDevices.add(
        DeviceSettingInfo().initWith({
          "device": "蜂窝网络",
          "value": NetWorkType.gsm,
          "selected": selectedNetwork == NetWorkType.gsm.name,
        }),
      );
    }
    return networkDevices;
  }

  Future<void> setMicrophoneConfig(
    DeviceSettingInfo setting, {
    bool enable = true,
  }) async {
    final audioSource = getAudioSourceDevice(name: setting.info);
    if (audioSource == null) return;
    await AppPreferences.microphoneSettings.setString(
      jsonEncode(setting.toMap()),
    );
    if (enable) {
      HostDevice.share.setAudioSourceVolume(audioSource, setting.value);
      currentMicrophone = setting;
      recordController.changeAudioSource(name: setting.info);
      rtcLiveController?.changeAudioSource(name: setting.info);
      rtmpLiveController?.changeAudioSource(name: setting.info);
      if (switchCamera.value) {
        rtcLive2Controller?.changeAudioSource(name: setting.info);
        rtmpLive2Controller?.changeAudioSource(name: setting.info);
      }
      smartVoicePrepare();
    }
  }

  Future<void> setLoudspeakerConfig(
    DeviceSettingInfo setting, {
    bool enable = true,
  }) async {
    final audioSink = getAudioSinkDevice(name: setting.info);
    if (audioSink == null) return;
    await AppPreferences.loudspeakerSettings.setString(
      jsonEncode(setting.toMap()),
    );
    if (enable) {
      app.logW('----------------设置的音量:${setting.value}');
      HostDevice.share.setAudioSinkVolume(audioSink, setting.value);
      currentLoudspeaker.value = setting;
      rtcLiveController?.changeAudioSink(name: setting.info);
      if (switchCamera.value) {
        rtcLive2Controller?.changeAudioSink(name: setting.info);
      }
      smartVoicePrepare();
    }
  }

  Future<void> setNetworkConfig(NetWorkType type) async {
    app.logW("设置的网络：${type.toString()}");
    ToastUtils.showToast(context!, message: "正在切换网络请稍等");
    await AppPreferences.selectedNetwork.setString(type.name);
    await AppContext.share.switchNetwork(type);
    ToastUtils.showToast(context!, message: "网络切换成功");
    networkTypeChange.value = !networkTypeChange.value;
  }

  //设置设备和音量会回调
  Future<void> audioSourcesChanged() async {
    List<ffi.AudioDevice> audios = List.from(
      HostDevice.share.audioSources.value,
    );

    audios.removeWhere(
      (audio) =>
          audio.description.dartString.startsWith("PRO CAPTURE AIO 4K PLUS"),
    );

    if (microphoneDevices.isEmpty &&
        HostDevice.share.audioSources.value.length > 1) {
      // 初始化时会回调此函数，避免覆盖选中配置
      getMicrophoneDevices();
      return;
    }

    DeviceSettingInfo? newDevice = checkExistNewDevice(
      microphoneDevices,
      audios,
    );
    if (newDevice != null) {
      await setMicrophoneConfig(newDevice, enable: false);
    } else if (microphoneDevices.length == audios.length) {
      return;
    }

    if (microphoneDevices.isNotEmpty && audios.isEmpty) {
      //音频列表检测兜底，避免插拔过程中底层音频列表返回为空（实际不为空，可能需要一点时间）,导致UI异常
      Future.delayed(2.seconds).then((value) {
        //设置设备并更新UI
        eventBus.fire(
          EventBusInfo(
            type: EventBusType.microphoneDeviceChange,
            data: getMicrophoneDevices(),
          ),
        );
      });
      return;
    }
    //设置设备并更新UI
    eventBus.fire(
      EventBusInfo(
        type: EventBusType.microphoneDeviceChange,
        data: getMicrophoneDevices(),
      ),
    );
  }

  Future<void> audioSinksChanged() async {
    if (loudspeakerDevices.isEmpty &&
        HostDevice.share.audioSinks.value.length > 1) {
      // 初始化时会回调此函数，避免覆盖选中配置
      getLoudspeakerDevices();
      return;
    }

    DeviceSettingInfo? newDevice = checkExistNewDevice(
      loudspeakerDevices,
      HostDevice.share.audioSinks.value,
    );
    if (newDevice != null) {
      await setLoudspeakerConfig(newDevice, enable: false);
    } else if (loudspeakerDevices.length ==
        HostDevice.share.audioSinks.value.length) {
      return;
    }

    if (loudspeakerDevices.isNotEmpty &&
        HostDevice.share.audioSinks.value.isEmpty) {
      //音频列表检测兜底，避免插拔过程中底层音频列表返回为空（实际不为空，可能需要一点时间）,导致UI异常
      Future.delayed(2.seconds).then((value) {
        eventBus.fire(
          EventBusInfo(
            type: EventBusType.loudspeakerDeviceChange,
            data: getLoudspeakerDevices(),
          ),
        );
      });
      return;
    }
    //设置设备并更新UI
    eventBus.fire(
      EventBusInfo(
        type: EventBusType.loudspeakerDeviceChange,
        data: getLoudspeakerDevices(),
      ),
    );
  }

  void videoDeviceChanged() {
    app.logW("获取的视频设备列表：${HostDevice.share.videoSources.value}");
    int lastVideoCount = videoDevices.length;
    //更新UI
    eventBus.fire(
      EventBusInfo(
        type: EventBusType.videoDeviceChange,
        data: getExtendedVideoDevices(),
      ),
    );
    if (lastVideoCount != videoDevices.length && currentVideoDevice != null) {
      setVideoDevice(currentVideoDevice!);
    }
  }

  /// 判断是否有新设备插入
  DeviceSettingInfo? checkExistNewDevice(
    List<DeviceSettingInfo> originalDevices,
    List<ffi.AudioDevice> newAudioDevices,
  ) {
    for (var audio in newAudioDevices) {
      DeviceSettingInfo? device =
          originalDevices
              .where((dev) => dev.info == audio.name.dartString)
              .lastOrNull;
      if (device == null) {
        return DeviceSettingInfo().initWith({
          "device": audio.description.dartString,
          "value": audio.volume > 100 ? 100 : audio.volume,
          "info": audio.name.dartString,
        });
      }
    }
    return null;
  }

  /// 获取当前音频输入设备名
  String getAudioSourceName() {
    int lastMicrophone = microphoneDevices.indexWhere(
      (device) => device.selected,
    );
    return lastMicrophone == -1
        ? 'default'
        : microphoneDevices[lastMicrophone].info;
  }

  /// 获取当前音频输出设备名
  String getAudioSinkName() {
    int lastLoudspeaker = loudspeakerDevices.indexWhere(
      (device) => device.selected,
    );
    return lastLoudspeaker == -1
        ? 'default'
        : loudspeakerDevices[lastLoudspeaker].info;
  }

  /// 通过设备名获取音频输入设备
  ffi.AudioDevice? getAudioSourceDevice({required String name}) {
    for (var audioSource in HostDevice.share.audioSources.value) {
      if (name == audioSource.name.dartString) return audioSource;
    }
    return null;
  }

  /// 通过设备名获取音频输出设备
  ffi.AudioDevice? getAudioSinkDevice({required String name}) {
    for (var audioSink in HostDevice.share.audioSinks.value) {
      //todo 设备名一致可能存在问题，后续优化
      if (name == audioSink.name.dartString) return audioSink;
    }
    return null;
  }

  void smartVoicePrepare() async {
    bool lastPrepare = voicePrepare;
    //语音助手准备
    voicePrepare = VoiceHelper.share.prepare(
      audioCaptureName: getAudioSourceName(),
      audioRenderName: getAudioSinkName(),
      //renderVolume: getTipVolume(getAudioSinkName()) / 65535, //提示音
      renderVolume:
          currentLoudspeaker.value == null
              ? 1.0
              : currentLoudspeaker.value!.value / 100,
    );

    if (!lastPrepare && voicePrepare) {
      addVoiceHandler();
      // 语音状态改变暂以协同直播状态触发小组件刷新
      roomAndLiveStatus.value = RoomAndLiveStatus(
        isRtcLiveOngoing: roomAndLiveStatus.value.isRtcLiveOngoing,
        isRtmpLiveOngoing: roomAndLiveStatus.value.isRtmpLiveOngoing,
      );
    }
  }

  /// -------功能控制区end-------

  void setMicrophoneState({required bool enabled}) {
    if (roomAndLiveStatus.value.isRtcLiveOngoing) {
      rtcLiveController?.speak(enabled);
      if (switchCamera.value) {
        rtcLive2Controller?.speak(enabled);
      }
    }
    if (roomAndLiveStatus.value.isRtmpLiveOngoing) {
      rtmpLiveController?.speak(enabled);
      if (switchCamera.value) {
        rtmpLive2Controller?.speak(enabled);
      }
    }
    microphoneIsOn.value = enabled;

    broadcastSettingStatus(ClientMsgType.microphoneState, enabled: enabled);

    ToastUtils.showToast(
      context!,
      message: enabled ? S.current.f_zONLgahY : S.current.f_zQ5FZSeB,
      type: ToastType.success,
    );
  }

  void showCurrentSpeaker(SpeakerMessage speaker) {
    int userId =
        speaker.userId >= 10000 ? speaker.userId ~/ 10000 : speaker.userId;
    final currentDoctor =
        doctors.value.where((e) => e.id == userId).firstOrNull;
    if (currentDoctor != null) {
      currentDoctor.speaking = speaker.isSpeaking;
      int milliseconds = DateTime.now().millisecondsSinceEpoch;
      currentDoctor.lastSpeakingTime = milliseconds;

      if (currentDoctor.speaking) {
        currentDoctor.isMicrophoneActive = true;
      }
      doctors.value = [...doctors.value];
    }
  }

  void changeSynergyMicrophone(MicrophoneMessage microphoneMessage) {
    final currentDoctor =
        doctors.value
            .where((e) => e.id == microphoneMessage.userId)
            .firstOrNull;
    if (currentDoctor != null) {
      currentDoctor.isMicrophoneActive = !microphoneMessage.muted;
      doctors.value = [...doctors.value];
    }
  }

  UserGraphic getOrCreateGraphic(int userId) {
    var userGraphic =
        graphics.value.where((g) => g.userId == userId).firstOrNull;
    final doctor = doctors.value.where((d) => d.id == userId).firstOrNull;
    if (userGraphic == null) {
      userGraphic = UserGraphic().copyWith(
        userId: userId,
        name: doctor?.name ?? '-',
      );
      graphics.value.add(userGraphic);
    } else {
      userGraphic.userId = userId;
      userGraphic.name = doctor?.name ?? '-';
    }
    return userGraphic;
  }

  /// 上传术中截图
  Future<void> upOperationScreenshot() async {
    if (AppContext.share.networkConnected.value) {
      CosCredential screenshotCredential =
          await HttpCosApi<CosCredential>.getScreenshotCosCredential(
            deviceId: AppContext.share.authorizeInfo.deviceId,
          ).request();
      final patterns = screenshotCredential.bucket.split('-');
      if (patterns.length < 2) {
        return;
      }
      final appId = patterns.removeLast();
      final bucketName = patterns.join("-");
      COSApiFactory.initialize(
        config: COSConfig(
          appId: appId,
          secretId: screenshotCredential.credentials.accessKeyId,
          secretKey: screenshotCredential.credentials.secretAccessKey,
        ),
        bucketName: bucketName,
        region: screenshotCredential.region,
      );

      final imageBytes = await videoScreenshotController.capture(
        pixelRatio: 0.1,
      );
      if (imageBytes == null) return;
      try {
        await COSApiFactory.objectApi.putObject(
          objectKey: screenshotCredential.key,
          headers: {
            'x-cos-security-token':
                screenshotCredential.credentials.sessionToken,
          },
          contentType: 'image/png',
          objectValue: imageBytes,
        );
        String res =
            "${COSApiFactory.objectApi.getBaseApiUrl()}/${screenshotCredential.key}";
        userCountOrAiPhaseChanged(isThumbnailUpdated: true);
        app.logW("上传结果：$res");
      } catch (e) {
        app.logE("上传异常：${e.toString()}");
      }
    }
  }

  /// 上传出血回放视频
  Future<String?> upBloodPlaybackVideo(String filePath) async {
    if (AppContext.share.networkConnected.value) {
      //检查cos配置请求失败情况
      CosCredential bleedingCredential =
          await HttpCosApi<CosCredential>.getCosCredential().request();
      final patterns = bleedingCredential.bucket.split('-');
      final appId = patterns.last;
      final bucketName = patterns.take(2).join('-');

      COSApiFactory.initialize(
        config: COSConfig(
          appId: appId,
          secretId: bleedingCredential.credentials.accessKeyId,
          secretKey: bleedingCredential.credentials.secretAccessKey,
        ),
        bucketName: bucketName,
        region: bleedingCredential.region,
      );
      try {
        String savePath =
            "${bleedingCredential.keyPrefix}/${DateTime.now().millisecondsSinceEpoch}.mp4";
        await COSApiFactory.objectApi.putFileObject(
          objectKey: savePath,
          filePath: filePath,
          headers: {
            'x-cos-security-token': bleedingCredential.credentials.sessionToken,
          },
        );
        String res = "${COSApiFactory.objectApi.getBaseApiUrl()}/$savePath";
        app.logW("上传结果：$res");

        return savePath;
      } catch (e) {
        app.logD("上传异常：${e.toString()}");
        return null;
      }
    }
    return null;
  }

  void subscriptLocationMqttMessage() {
    /// 出血消息监听
    onBleedingObserver = MqttDeviceAiApi<BleedingData>.onBleedingMessage(
      surgeryId: surgeryId,
    ).listen((message) {
      // app.logW("bleeding message:${message.toMap()}");
      bleedMsg(message);
    });
  }

  void bleedMsg(BleedingData message) {
    //  止血消息恢复UI,停止mqtt发送
    if (message.bleedingReplay == 9 &&
        playbackControl.value?.playbackType == PlaybackType.bleedPlay) {
      isBloodNotify = false;
      toggleBloodPlayback();
      return;
    }

    if (message.bleedingReplay == 1) {
      //正在出血或回放
      if (playbackControl.value != null) {
        return;
      }
      int startFrame = message.bleedingPointDetection.startFrame;

      if (startFrame <= 0 || message.bleedingPointDetection.positions.isEmpty) {
        return;
      }
      _bleedingInfo = message;

      // 获取出血视频回放路径
      recordController.merge(
        name: startFrame.toString(),
        beginTime: startFrame - 1500,
        endTime: startFrame + 1500,
        needEndPrecision: true,
      );
    }
  }

  bool isSurgPlaybackFile(String fileName) {
    return fileName.startsWith("surg_");
  }

  ///完成视频合并回调
  void onMergedVideo(ffi.VideoRecordInfo videoInfo) async {
    playbackMerging = false;
    app.logW("合成消息:${videoInfo.toString()}——${DateTime.now().toString()}");

    if (videoInfo.path.dartString.isEmpty) {
      if (isSurgPlaybackFile(videoInfo.name.dartString) &&
          showSurgPlayback.value) {
        ToastUtils.showToast(context!, message: "暂无可回放视频");
      }
      showSurgPlayback.value = false;
      return;
    }
    PlaybackInfo playInfo;
    if (isSurgPlaybackFile(videoInfo.name.dartString)) {
      //术中回放片段生成
      String videoPath = videoInfo.path.dartString;
      int videoDuration = (videoInfo.frameCount / 60.0 * 1000).toInt();
      File file = File(videoPath);
      if (videoPath.endsWith(".mp4")) {
        videoPath =
            "${videoPath.substring(0, videoPath.length - 4)}_$videoDuration.mp4";
        file.renameSync(videoPath);
      }

      if (!showSurgPlayback.value) {
        return;
      }
      //术中回放合成
      playbackList.add(
        MergeVideoInfo(duration: videoDuration, path: videoPath),
      );

      int totalDuration = 0;
      for (var info in playbackList) {
        if (info.duration == 0) {
          //避免文件名写入失败导致无法获取时长
          double videoDuration = await DeviceCmd.share.getVideoDuration(
            path: info.path,
          );
          info.duration = (videoDuration * 1000).toInt();
        }
        totalDuration += info.duration;
      }
      showSurgPlayback.value = false;

      int referenceDuration = 1000 * 60 * 3; //3分钟
      playInfo = PlaybackInfo(
        videoInfo: playbackList,
        playbackType: PlaybackType.surgPlay,
        startPlayMs:
            totalDuration > referenceDuration
                ? (totalDuration - referenceDuration)
                : 0,
      );
    } else {
      ///注：数据为30帧，视频为60帧，注意计算
      int videoPointOffset = videoInfo.pointOffset;
      int frameCount = videoInfo.frameCount;
      BleedingPoint bleedingPoint;
      if (frameCount > 60 && frameCount < 10000) {
        //目前硬编码3s180祯左右，这里容错10000祯，防止由于数据异常补祯过大崩溃
        BleedingPoint data = _bleedingInfo!.bleedingPointDetection;
        int startFrameIndex = data.positions.indexWhere((p) {
          return p.isNotEmpty &&
              (p.first.frame == data.startFrame ||
                  p.last.frame == data.startFrame);
        });
        if (startFrameIndex == -1) {
          //没有找到算法出血起始帧，说明数据异常
          return;
        }
        //如果算法出血偏移帧大于视频偏移，则过滤掉多余的帧
        if (startFrameIndex > videoInfo.pointOffset ~/ 2) {
          int overflowIndex = startFrameIndex - videoInfo.pointOffset ~/ 2;
          data.positions = data.positions.sublist(overflowIndex);
          videoPointOffset = 0;
        } else {
          //调整视频偏移帧，使其与算法偏移帧一致
          videoPointOffset = videoPointOffset - startFrameIndex * 2;
        }
        bleedingPoint = repairPoint(
          point: data,
          fillFrameNum: (frameCount - videoPointOffset) ~/ 2,
        );
      } else {
        //视频不足1s,判为错误无法向下执行
        return;
      }

      playbackList = [];
      playbackList.add(
        MergeVideoInfo(
          path: videoInfo.path.dartString,
          duration: ((videoInfo.frameCount / 60.0) * 1000).toInt(),
        ),
      );

      playInfo = PlaybackInfo(
        playbackType: PlaybackType.bleedPlay,
        videoInfo: playbackList,
        bleedingPoint: bleedingPoint,
        offsetMs: (videoPointOffset * 16.7).round(),
      );
    }

    // UI 播放切换
    toggleBloodPlayback(playInfo: playInfo);
    app.logW(
      "total: ${playInfo.getTotalDuration()}, startPlayMs: ${playInfo.startPlayMs}",
    );
  }

  /// 追踪退出
  void trackingExit() {
    if (trackingExitCount >= 1) {
      if (context != null) {
        Modal(
          kind: ModalKind.dialog,
          type: ModalType.info,
          closable: true,
          title: "提示",
          message: "检测到您多次退出该功能,是否在本次手术期间临时关闭",
          cancelText: "仅退出，保持开启",
          confirmText: "本次手术关闭",
          onCancel: () {
            trackingExitCount++;
            trackingExitByUser = true;
            isTracking.value = false;
            context?.pop();
          },
          onConfirm: () {
            trackingExitCount = -1;
            trackingExitByUser = true;
            isTracking.value = false;
            context?.pop();
          },
        ).show(context!);
      }
    } else {
      trackingExitCount++;
      trackingExitByUser = true;
      isTracking.value = false;
    }
  }

  /// playInfo 传递此参数则开始视频回放UI，否则关闭回放UI
  void toggleBloodPlayback({PlaybackInfo? playInfo}) {
    for (final _ in AppRouteObserver.share.popups) {
      context?.pop();
    }
    // 避免悬浮框显示
    eventBus.fire(EventBusInfo(type: EventBusType.closeSelect));
    playbackControl.value = playInfo;
    showSurgPlayback.value = false;

    if (playInfo == null) {
      Throttle.instance.checkPass("playbackExit", intervalMs: 2000);
      playbackLargeScreenSync.value = false;
    } else if (playInfo.playbackType == PlaybackType.surgPlay) {
      //术中回放自动同步大屏
      playbackLargeScreenSync.value = true;
    } else if (playInfo.playbackType == PlaybackType.bleedPlay) {
      Future.delayed(1.seconds).then((_) {
        playbackKey.currentState?.setBleedingPointsDrawListener((
          List<Offset> points,
        ) {
          sendBleedingPointsToWindows(points);
        });
      });
    }
    // 发送状态到扩展屏幕
    sendSurgeryStatusToWindows();
  }

  /// 绘制点补足视频长度
  BleedingPoint repairPoint({
    required BleedingPoint point,
    required int fillFrameNum,
  }) {
    //服务器数据容错处理
    if (point.positions.length >= fillFrameNum) {
      return point;
    }

    //bool repairFirst = true;
    while (point.positions.length < fillFrameNum) {
      // if (repairFirst) {
      //   point.positions.insert(0, point.positions.first); //视频开始帧已对齐，往后补帧
      // } else {
      point.positions.add(point.positions.last);
      // }
      // repairFirst = !repairFirst;
    }
    return point;
  }

  void createTaskTimer() {
    taskTimer = Timer.periodic(1.seconds, (timer) {
      //利用此Timer定时发送出血通知
      if (isBloodNotify) {
        // if (AppContext.share.controlToken.last?.isNotEmpty == true) {
        //   MqttDeviceControlApi.broadcastBleedingStatus(
        //     controlToken: AppContext.share.controlToken.last!,
        //     surgeryId: surgeryId,
        //     isBleeding: true,
        //   ).publish();
        // }
      }

      final now = DateTime.now();

      //修复web多端同时开麦说话状态异常问题，后期通过agora优化
      if (lastSpeakCheckTime == 0 ||
          now.millisecondsSinceEpoch - lastSpeakCheckTime >= 3000) {
        lastSpeakCheckTime = now.millisecondsSinceEpoch;
        //mq2s间隔发送音量，3s后未检测到说话则认为未说话
        bool updateSpeak = false;
        for (var item in doctors.value) {
          if (item.speaking &&
              now.millisecondsSinceEpoch - item.lastSpeakingTime > 3000) {
            item.speaking = false;
            updateSpeak = true;
          }
        }
        if (updateSpeak) {
          doctors.value = [...doctors.value];
        }
      }

      if (lastUpTime == 0 || now.millisecondsSinceEpoch - lastUpTime >= 30000) {
        //间隔30s上传手术截图
        lastUpTime = now.millisecondsSinceEpoch;
        upOperationScreenshot();
      }

      for (var g in graphics.value) {
        DrawingBoardNotifier.removeGraphic(g, now);
      }

      graphics.value = [...graphics.value];

      if (roomAndLiveStatus.value.isRtmpLiveOngoing &&
          liveUserRemainCounter == 0) {
        HttpLiveApi<LiveInfo>.userCount(
          deviceId: AppContext.share.authorizeInfo.deviceId,
        ).request().then((value) => liveUserCount.value = value.count);
      }
      liveUserRemainCounter = (liveUserRemainCounter + 1).remainder(5);

      // 发送状态到扩展屏幕
      sendSurgeryStatusToWindows();

      /// 检查回放视频列表
      checkPlaybackVideoList();
    });
  }

  void subscriptRoomEventBus() {
    // 订阅手术开启
    powerOffObserver = eventBus.on<EventBusInfo>().listen((event) {
      if (event.type == EventBusType.powerOff && loadingStatus == null) {
        stopSurgery(shutDown: true);
      }
    });
    // 订阅外置屏幕插入移除事件
    largeScreenEnable.value = MultiWindow.extensionScreens().isNotEmpty;

    screenObserver = eventBus.on<EventBusInfo>().listen((event) {
      if (event.type == EventBusType.screenEvent) {
        largeScreenEnable.value = event.data;
        //screenshotNow = false;
      } else if (event.type == EventBusType.onScreenRemove) {
        largeScreenEnable.value = false;
        List<String> closeNames = event.data;
        for (var name in closeNames) {
          textureRenderersAddress.remove(name);
          if (allTextureRenderersAddress[name] != null) {
            removeRender(allTextureRenderersAddress[name]);
          }
        }
      }
    });
  }

  Future<bool> checkPlaybackVideoList({bool immediate = false}) async {
    if (playbackMerging) return false; // 避免同时触发多次合并保证视频片段正确性，此处认为merge回调动作是可靠的
    int milliseconds = DateTime.now().millisecondsSinceEpoch;
    int beginTime = state.value?.surgeryTime ?? 0;
    int subsectionDuration = 1000 * 60 * 10;

    if (_lastMergeTime == 0) {
      _lastMergeTime = milliseconds;
    }
    if (immediate || milliseconds - _lastMergeTime > subsectionDuration) {
      /// 每10分钟合并一次
      // 获取最近文件记录的结束合并时间作为本次开始时间
      final documents = await getApplicationDocumentsDirectory();
      // todo 此处暂为固定生成目录，如有变更请同步修改
      String playbackDirectory =
          '${documents.path}/v202310/$surgeryId/playback/';

      final directory = Directory(playbackDirectory);

      final SplayTreeMap<int, MergeVideoInfo> fileMap =
          SplayTreeMap<int, MergeVideoInfo>();
      playbackList = [];
      if (directory.existsSync()) {
        /// 查找已生成的回放片段文件，surg_开头
        directory.listSync(recursive: false, followLinks: false).forEach((
          fileSystemEntity,
        ) {
          if (fileSystemEntity is File) {
            int index = fileSystemEntity.path.lastIndexOf('/');
            if (index == -1 || !fileSystemEntity.path.endsWith('.mp4')) return;
            String name = fileSystemEntity.path.substring(
              index + 1,
              fileSystemEntity.path.length - 4,
            );
            if (!isSurgPlaybackFile(name)) return;
            List<String> names = name.split('_');
            if (names.length != 4) return;

            int time = int.tryParse(names[2]) ?? 0;
            int duration = int.tryParse(names[3]) ?? 0;
            if (time == 0 || duration == 0) return;
            time += 1;
            if (!immediate && milliseconds - time < subsectionDuration) {
              return;
            }
            fileMap[time] = MergeVideoInfo(
              path: fileSystemEntity.path,
              duration: duration,
            );
          }
        });

        if (fileMap.isNotEmpty) {
          beginTime = fileMap.lastKey()!;
          playbackList = fileMap.values.toList();
        }
      }
      _lastMergeTime = milliseconds;
      playbackMerging = true;
      recordController.merge(
        name: 'surg_${beginTime}_$milliseconds',
        beginTime: beginTime,
        endTime: milliseconds,
        needEndPrecision: false,
      );
      return true;
    }
    return false;
  }

  void playAudio({required String fileName}) {
    String path =
        '~/surgsmart-operating-room/data/flutter_assets/packages/surgsmart/assets/texts/medias/$fileName';
    DeviceCmd.share.playAudio(
      deviceName: currentLoudspeaker.value?.info,
      audioPath: path,
      //volume: getTipVolume(currentLoudspeaker.value?.info).toInt()); //提示音
      volume:
          currentLoudspeaker.value == null
              ? 65535
              : (currentLoudspeaker.value!.value / 100 * 65535).toInt(),
    );
  }

  @override
  VoiceTask? hitTest(String command) {
    if (!voicePrepare) {
      return null;
    }
    AppVoice.show(state: DisplayState.listening, content: command);
    return checkCommand(command);
  }

  VoiceTask? checkCommand(String command) {
    List<String> markKeys = [
      '截图',
      '截个图',
      '截下图',
      '截屏',
      '截个屏',
      '截下屏',
      '截取画面',
      '标记',
      '添加睿标记',
      '截一下',
    ];
    List<String> inviteKeys = ['邀请', '进手术协同', '进协同', '进手术室', '上线', '呼叫'];
    List<String> exitKeys = ['退出', '退下', '关闭', '关了', '不用了', '回去', '闭嘴'];
    const hintText = "抱歉，我不太明白";

    return VoiceTask((command) async {
      if (app.existedOverlay(tag: userSelectPanelTag)) {
        // 选择弹框中出现退出指令
        String content = hintText;
        if (exitKeys.indexWhere((key) {
              return command.contains(key);
            }) !=
            -1) {
          userSelectPanel(hint: command, show: false, seconds: 2);
          return VoiceTaskResult(remindWords: "好的");
        }
        //回显语音识别
        userSelectPanel(
          hint: command,
          show: true,
          labels: inviteUsers.map((e) => e.name).toList(),
        );

        int? num = parseNumber(command);
        if (num != null && num <= inviteUsers.length) {
          num = num - 1;
          if (await invitationUser(inviteUsers[num].id)) {
            content = "好的，已向${inviteUsers[num].name}医生发送邀请";
          } else {
            num = null;
          }
        }
        await Future.delayed(1.seconds);
        userSelectPanel(
          show: true,
          hint: content,
          labels: inviteUsers.map((e) => e.name).toList(),
          seconds: 3,
          selectedIndex: num,
        );
        return VoiceTaskResult(remindWords: content);
      }

      if (markKeys.indexWhere((key) {
            return command.contains(key);
          }) !=
          -1) {
        await Future.delayed(1.seconds);
        const content = "好的，已为您截图";
        markTime();
        AppVoice.show(
          state: DisplayState.success,
          content: content,
          delayedSeconds: 2,
        );
        return const VoiceTaskResult(remindWords: content);
      } else if (inviteKeys.indexWhere((key) {
            return command.contains(key);
          }) !=
          -1) {
        List<InvitationUser> users = checkInvite(command);
        if (users.length > 6) {
          users.sort((a, b) => a.id.compareTo(b.id));
          users = users.sublist(0, 6);
        }
        if (users.isEmpty) {
          await Future.delayed(2.seconds);
          const content = "抱歉，没有找到这位医生";
          AppVoice.show(
            state: DisplayState.fail,
            content: content,
            delayedSeconds: 3,
          );
          return const VoiceTaskResult(remindWords: content);
        }
        if (users.length == 1) {
          String content = hintText;
          if (await invitationUser(users.first.id)) {
            content = "好的，已向${users.first.name}医生发送邀请";
          }
          AppVoice.show(
            state: DisplayState.success,
            content: content,
            delayedSeconds: 3,
          );
          return VoiceTaskResult(remindWords: content);
        } else {
          String content =
              isInviteDirector
                  ? "找到${users.length}位科室主任，您想邀请第几位？"
                  : "找到${users.length}位同名医生，您想邀请第几位？";
          inviteUsers = users;
          userSelectPanel(
            show: true,
            hint: content,
            labels: users.map((e) => e.name).toList(),
          );
          VoiceHelper.share.speaking(content);
          await Future.delayed(4.seconds);
          return VoiceTaskResult(state: VoiceTaskState.next);
        }
      } else if (exitKeys.indexWhere((key) {
            return command.contains(key);
          }) !=
          -1) {
        const content = "好的";
        AppVoice.show(
          state: DisplayState.success,
          content: content,
          delayedSeconds: 3,
        );
        return const VoiceTaskResult(remindWords: content);
      } else {
        await Future.delayed(2.seconds);
        AppVoice.show(
          state: DisplayState.fail,
          content: hintText,
          delayedSeconds: 3,
        );
        return const VoiceTaskResult(remindWords: hintText);
      }
    });
  }

  void addVoiceHandler() {
    VoiceHelper.share.addHandler(this);
    VoiceHelper.share.config(
      onAwakened: (greetingWords) {
        if (!voicePrepare) return;
        userSelectPanel(show: false);
        getInvitationInfo();
        AppVoice.show(state: DisplayState.activate, content: greetingWords);
      },
      onRecognizing: (commandWords, finish) {
        if (app.existedOverlay(tag: userSelectPanelTag)) {
          return;
        }
        AppVoice.show(state: DisplayState.listening, content: commandWords);
      },
      onError: (state, speakWords) async {
        if (!voicePrepare) return;
        await Future.delayed(1.seconds);

        userSelectPanel(show: false);
        if (state == VoiceCommandState.unRecognize) {
          AppVoice.dismiss();
          app.logW("识别onError unRecognize");
        } else {
          app.logW("识别onError $speakWords");
          AppVoice.show(
            state: DisplayState.fail,
            content: speakWords.isEmpty ? "抱歉我不太明白" : speakWords,
            delayedSeconds: 3,
          );
        }
      },
    );
  }

  Future<void> getInvitationInfo() async {
    invitationInfo =
        await HttpRoomApi<InvitationInfo>.getInvitationList(
          deviceId: AppContext.share.authorizeInfo.deviceId,
          orgId: AppPreferences.department.intValue ?? 0,
        ).request();
    invitationInfo?.datas.forEach((element) {
      element.namePinyin = PinyinHelper.getPinyin(element.name);
    });
  }

  Future<bool> invitationUser(int userId) async {
    try {
      await HttpRoomApi.invitationUser(
        deviceId: AppContext.share.authorizeInfo.deviceId,
        userId: userId,
      ).request();
      return true;
    } catch (e) {
      return false;
    }
  }

  // 标记当前是否是邀请主任动作
  bool isInviteDirector = false;

  List<InvitationUser> checkInvite(String command) {
    List<InvitationUser> inviteUsers = [];
    List<String> inviteNames = [];
    isInviteDirector = false;

    /// 为提高准确率非严格按照prd关键词设置

    List<String> directorKeys = [
      /* prd */
      // '邀请主任',
      // '请主任进手术协同',
      // '请主任进协同',
      // '请主任进手术室',
      // '请主任上线',
      // '呼叫主任',

      /* 如prd 建议补充 */
      // '请主任进入手术协同',
      // '请主任进入协同',
      // '请主任协同',
      // '请主任手术协同',
      // '请主任进入手术室',

      // 优化词(如后续扩展其他功能存在冲突则需优化)
      '请主任',
      '请科室主任',
      '呼叫主任',
      '呼叫科室主任',
      '请主人', //谐音容错
      '呼叫主人', //谐音容错
    ];

    int index = directorKeys.indexWhere((key) => command.contains(key));
    if (index != -1) {
      invitationInfo?.datas.forEach((user) {
        if (user.isMyDepartmentDirector) {
          inviteUsers.add(user);
        }
      });

      if (inviteUsers.isNotEmpty) {
        isInviteDirector = true;
        return inviteUsers;
      }
    }

    List<String> revereKeys = [
      '医生进手术协同',
      '医生进协同',
      '医生进手术室',
      '医生上线',
      '医生进入手术协同', //新增标记
      '医生进入协同', //
      '医生协同', //
      '医生手术协同', //
      '医生进入手术室', //
      '主任进手术协同',
      '主任进协同',
      '主任进手术室',
      '主任上线',
      '主任进入手术协同', //
      '主任进入协同', //
      '主任协同', //
      '主任手术协同', //
      '主任进入手术室', //
      '院长进手术协同',
      '院长进协同',
      '院长进手术室',
      '院长上线',
      '院长进入手术协同', //
      '院长进入协同', //
      '院长协同', //
      '院长手术协同', //
      '院长进入手术室', //
      '老师进手术协同',
      '老师进协同',
      '老师进手术室',
      '老师上线',
      '老师进入手术协同', //
      '老师进入协同', //
      '老师协同', //
      '老师手术协同', //
      '院长进入手术室', //
      '专家进手术协同',
      '专家进协同',
      '专家进手术室',
      '专家上线',
      '专家进入手术协同', //
      '专家进入协同', //
      '专家协同', //
      '专家手术协同', //
      '专家进入手术室', //
    ];

    inviteNames = parseNamePinyin(
      command: command,
      keys: revereKeys,
      isStartWith: true,
    );
    app.logW("inviteNames：$inviteNames");

    if (inviteNames.isNotEmpty) {
      inviteUsers = matchNamePinyin(invitationInfo, inviteNames);
      app.logW(
        "inviteNames：${invitationInfo?.datas.map((item) => item.namePinyin).toList()}}",
      );

      if (inviteUsers.isNotEmpty) return inviteUsers;
    }

    List<String> endKeys = [
      '进入手术', //
      '进手术', //
      '进协同',
      '进入协同', //
      '上线',
      '协同', //
    ];
    inviteNames = parseNamePinyin(
      command: command,
      keys: endKeys,
      isStartWith: true,
    );
    if (inviteNames.isNotEmpty) {
      inviteUsers = matchNamePinyin(invitationInfo, inviteNames);
      if (inviteUsers.isNotEmpty) return inviteUsers;
    }

    List<String> startKeys = ['邀请', '呼叫'];
    inviteNames = parseNamePinyin(
      command: command,
      keys: startKeys,
      isStartWith: false,
    );
    if (inviteNames.isNotEmpty) {
      inviteUsers = matchNamePinyin(invitationInfo, inviteNames);
      if (inviteUsers.isNotEmpty) return inviteUsers;
    }
    return inviteUsers;
  }

  final userSelectPanelTag = "userSelectPanel";
  List<InvitationUser> inviteUsers = []; //暂存识别的多用户

  void userSelectPanel({
    String? hint,
    List<String>? labels,
    required bool show,
    int? selectedIndex,
    int? seconds,
  }) {
    AppVoice.dismiss();
    if (show) {
      app.addOverlay(
        tag: userSelectPanelTag,
        child: Positioned.fill(
          child: Center(
            child: VoiceUserSelector(
              hint: hint ?? "",
              labels: labels ?? [],
              selectedIndex: selectedIndex,
            ),
          ),
        ),
      );
      if (seconds != null) {
        Future.delayed(Duration(seconds: seconds)).then((value) {
          app.removeOverlay(tag: userSelectPanelTag);
        });
      }
    } else {
      inviteUsers.clear();
      app.removeOverlay(tag: userSelectPanelTag);
    }
  }

  /// isStartWith true：向前匹配名称 false：向后匹配名称
  List<String> parseNamePinyin({
    required String command,
    required List<String> keys,
    required bool isStartWith,
  }) {
    List<String> names = [];
    for (var key in keys) {
      int index = command.indexOf(key);
      if (isStartWith) {
        //未查询到或小于2字不处理
        if (index < 2) {
          continue;
        }
        String name1 = command.substring(index - 2, index);
        try {
          names.add(PinyinHelper.getPinyin(name1));
          if (index > 2) {
            String name2 = command.substring(index - 3, index);
            names.add(PinyinHelper.getPinyin(name2));
          }
        } catch (e) {
          app.logE("$name1拼音转换err: $e ");
        }
      } else {
        if (index == -1) {
          continue;
        }
        index = index + key.length;
        if (command.length < index + 2) {
          continue;
        }
        String name1 = command.substring(index, index + 2);
        try {
          names.add(PinyinHelper.getPinyin(name1));
          if (command.length > index + 2) {
            String name2 = command.substring(index, index + 3);
            names.add(PinyinHelper.getPinyin(name2));
          }
        } catch (e) {
          app.logE("$name1拼音转换err: $e ");
        }
      }
    }
    return names;
  }

  List<InvitationUser> matchNamePinyin(
    InvitationInfo? invitationInfo,
    List<String> inviteNames,
  ) {
    Map<String, InvitationUser> usersMap = {};
    if (invitationInfo?.datas.isNotEmpty != true) {
      return [];
    }
    invitationInfo?.datas.forEach((user) {
      for (var name in inviteNames) {
        if (user.namePinyin == null) continue;
        //兼容前后鼻音
        String namePinyin = user.namePinyin!.substring(
          0,
          user.namePinyin!.length - 1,
        );
        if (name.startsWith(namePinyin)) {
          usersMap[user.name] = user;
        }
      }
    });
    return usersMap.values.toList();
  }

  int? parseNumber(String command) {
    RegExp regExp = RegExp(r'^[^\d]*\d[^\d]*$');
    if (!regExp.hasMatch(command)) {
      return null;
    }
    if (command.contains('1') || command.contains('一')) {
      return 1;
    } else if (command.contains('2') || command.contains('二')) {
      return 2;
    } else if (command.contains('3') || command.contains('三')) {
      return 3;
    } else if (command.contains('4') || command.contains('四')) {
      return 4;
    } else if (command.contains('5') || command.contains('五')) {
      return 5;
    } else if (command.contains('6') || command.contains('六')) {
      return 6;
    }
    return null;
  }

  // void startCapture({int fps = framerate}) async {
  //   _captureTimer ??= Timer.periodic(Duration(milliseconds: 1000 ~/ fps), (
  //     timer,
  //   ) async {
  //     // 大屏可用
  //     if (largeScreenEnable.value && HomeController.isSendImage) {
  //       screenDisplaySync();
  //     }
  //   });
  // }

  // Future<void> stopCapture() async {
  //   _captureTimer?.cancel();
  //   _captureTimer = null;
  //   attachAudioHandler(audioRecording: false, isRtmpLiveOngoing: false);

  //   return Future.delayed(Duration(seconds: 1));
  // }

  Future<void> pushLiveVideoFrame(ui.Image image) async {
    List<OverlayImageInfo> overlayImages = [];
    for (var graphic in graphics.value) {
      if (graphic.cursor?.offset != null) {
        overlayImages.add(
          OverlayImageInfo(
            image: AppContext.share.fingerSmall,
            graphic: graphic.visible,
            offset: graphic.cursor!.offset,
            label: graphic.name ?? '',
          ),
        );
      }
    }

    ByteData? rgbaData = await image.toByteData(
      format: ui.ImageByteFormat.rawRgba,
    );
    if (rgbaData == null) return;

    if (overlayImages.isNotEmpty) {
      image = await ImageUtil.overlayImageOnCanvas(
        bgImage: image,
        overlayImages: overlayImages,
      );
      rgbaData = await image.toByteData(format: ui.ImageByteFormat.rawRgba);
    }
    if (rgbaData == null) return;

    liveSharedBuffer
        .asTypedList(rgbaData.lengthInBytes)
        .setAll(0, rgbaData.buffer.asUint8List());
    rtmpLiveController?.pushCustomRgbaVideo(
      liveSharedBuffer,
      rgbaData.lengthInBytes,
      image.width,
      image.height,
    );
  }

  // Future<void> screenDisplaySync() async {
  //   //如果开启回放同步，则获取回放画面到大屏,否则获取手术画面到大屏
  //   if (!playbackLargeScreenSync.value || screenshotNow) return;

  //   screenshotNow = true; //截图太快会导致各种异常，所以加一个标志位
  //   final data = await playbackKey.currentState?.getPlayer()?.screenshot(
  //     format: null,
  //   );
  //   screenshotNow = false;

  //   int width = playbackKey.currentState?.videoWidth ?? 0;
  //   int height = playbackKey.currentState?.videoHeight ?? 0;

  //   if (data == null || width == 0 || height == 0) {
  //     return;
  //   }

  //   // 转换 BGRA 到 RGBA 格式并直接裁剪到 sharedBuffer
  //   final croppedSize = sharedBuffer.convertAndCrop(data, width, height);

  //   for (var address in textureRenderersAddress) {
  //     ffi.textureRendererRenderRgbaFrame(
  //       ffi.Pointer.fromAddress(address),
  //       sharedBuffer,
  //       croppedSize,
  //       width,
  //       height,
  //     );
  //   }
  // }

  /// 组装扩展屏幕需要的状态信息
  void sendSurgeryStatusToWindows() async {
    if (!largeScreenEnable.value) return;
    Map<String, dynamic> status = {};
    status["surgery_is_start"] = !_markSurgeryStop;
    status["video_is_fuzzy"] = outsideBlurIsOn.value && isBodyout.value;
    status["video_bitrate"] = videoBitrate.value;
    status["doctors"] =
        doctors.value.map((element) {
          return element.toMap();
        }).toList();
    status["live_user_count"] = liveUserCount.value;
    status["has_signal"] = !isShowNoSignalDialog;
    status["is_playback"] =
        playbackControl.value != null && playbackLargeScreenSync.value;
    status["playback_type"] = playbackControl.value?.playbackType.name;
    status["external_video_device"] =
        (switchCamera.value &&
                isShowExternalVideo.value &&
                currentVideoDevice != null)
            ? (currentVideoDevice!.info as ffi.VideoCaptureDevice)
                .path
                .dartString
            : null;
    status["procedure_name"] = surgeryInfo.procedure?.name;
    status["network_connected"] = AppContext.share.networkConnected.value;
    status["network_type"] = AppPreferences.selectedNetwork.stringValue;
    status["process_duration"] = playbackKey.currentState?.getProcessDuration();

    if (playbackLargeScreenSync.value) {
      textureRenderersAddress = await MultiWindow.sendAllWindowMsg(
        method: ExpandScreenController.videoFrameInfoTag,
      );
      // allTextureRenderersAddress.addAll(textureRenderersAddress);

      // for (var address in textureRenderersAddress.values) {
      //   playbackKey.currentState?.addRender(address);
      // }
    }

    MultiWindow.sendAllWindowMsg(
      method: ExpandScreenController.statusInfoTag,
      arguments: status,
    );
  }

  void sendInteractionMessageToWindows(String type, dynamic message) {
    if (!largeScreenEnable.value) return;
    Map<String, dynamic> msg = message.toMap();
    msg['type'] = type;
    MultiWindow.sendAllWindowMsg(
      method: ExpandScreenController.drawingBoardNotifierTag,
      arguments: msg,
    );
  }

  void sendBleedingPointsToWindows(List<ui.Offset> points) {
    if (!largeScreenEnable.value && !playbackLargeScreenSync.value) return;
    List<Map<String, double>> args =
        playbackKey.currentState?.getRealScale() != 1.0
            ? []
            : points
                .map((offset) => {'dx': offset.dx, 'dy': offset.dy})
                .toList();
    MultiWindow.sendAllWindowMsg(
      method: ExpandScreenController.drawingBleedingPointNotifierTag,
      arguments: args,
    );
  }

  var isStartShowModel = false;
  void updateEventFlags(int? eventFlags) async {
    _eventFlags = eventFlags ?? 0;

    if (_eventFlags == 0 && modelPlayer.state.completed) {
      modelPlayer.open(medias[0], play: false);
      return;
    }
    if (_eventFlags > 0 &&
        !isStartShowModel &&
        !modelPlayer.state.playing &&
        !modelPlayer.state.completed) {
      isStartShowModel = true;
      if (_eventFlags == 1) {
        modelPlayer.open(medias[0]);
      } else {
        modelPlayer.open(Playlist([medias[0], medias[_eventFlags - 1]]));
      }
      return;
    }
    if (_eventFlags > 1 && modelPlayer.state.completed) {
      modelPlayer.open(medias[isTracking.value ? _eventFlags - 1 : 1]);
      return;
    }
  }

  void sendTrackingEventsMessageToWindows(int eventFlags) async {
    if (!largeScreenEnable.value) {
      return;
    }

    final msg = {
      "arteryTracing": false,
      "arteryIdentification": false,
      "veinIdentification": false,
      "onlyEventFlags": true,
      "eventFlags": eventFlags,
    };

    final address = await MultiWindow.sendAllWindowMsg(
      method: ExpandScreenController.trackingInfoTag,
      arguments: msg,
    );

    // for (var entry in address.entries) {
    //   Map<String, int> renders = entry.value.cast<String, int>();
    //   modelVideoController.addRender(
    //     render: renders['modelRender']!,
    //     renderFunction: ffi.addresses.textureRendererRenderRgbaFrame.address,
    //   );
    // }
    updateEventFlags(eventFlags);
  }

  // 协同音频录制和直播推送
  void attachAudioHandler({bool? audioRecording, bool? isRtmpLiveOngoing}) {
    rtcLiveController?.attachRemoteAudioHandler(
      nativeRecorder:
          audioRecording ?? switchRecording.value
              ? recordController.nativeRecorder
              : ffi.nullptr,
      nativeRtmpLive:
          isRtmpLiveOngoing ?? roomAndLiveStatus.value.isRtmpLiveOngoing
              ? rtmpLiveController?.nativeRtmpLive
              : ffi.nullptr,
    );
  }

  void createRtc() {
    AgoraInfo agora = _roomAuthorizeInfo!.agora;
    rtcLiveController = RtcLive(
      videoCaptureDevice: getDefaultVideoCapture()!,
      streamType: StreamType.main,
      audioSourceName: getAudioSourceName(),
      audioSinkName: getAudioSinkName(),
      appId: agora.appId,
      token: agora.token,
      channelId: agora.channelName,
      userId: agora.uid.toString(),
      onUserJoined: (userId) async {
        int userCount = doctors.value.length;
        // 刷新用户列表
        doctors.value =
            (await HttpRoomApi<DoctorListInfo>.users(
                  deviceId: AppContext.share.authorizeInfo.deviceId,
                ).request())
                .datas;
        if (userCount == 0 && doctors.value.isNotEmpty) {
          playAudio(fileName: "enter_meeting.wav.txt");
        }
        // 显示新入用户
        final currentDoctor = doctors.value.firstWhere(
          (e) => e.id == userId,
          orElse: () => DoctorInfo().initWith({}),
        );
        if (currentDoctor.id != -1) currentDoctor.isNewJoin = true;
      },
      onUserLeft: (userId, reason) async {
        // 刷新用户列表
        doctors.value =
            (await HttpRoomApi<DoctorListInfo>.users(
                  deviceId: AppContext.share.authorizeInfo.deviceId,
                ).request())
                .datas;
      },
      onTransportStats: (rtcStats) {
        agoraRtcStats = rtcStats;
        videoBitrate.value = rtcStats.sendVideoBitrate;
      },
      onReInit: () async {
        rtcLiveController?.dispose();
        await createRtcController();
        rtcLiveController?.init();
        attachAudioHandler();

        if (doctors.value.isEmpty) {
          rtcLiveController?.stop();
        }
      },
    );

    /// todo 这里有问题先尝试模拟手动点击解决
    Future.delayed(Duration(seconds: 4)).then((_) {
      int lastLoudspeaker = loudspeakerDevices.indexWhere(
        (device) => device.selected,
      );
      if (lastLoudspeaker != -1) {
        setLoudspeakerConfig(loudspeakerDevices[lastLoudspeaker]);
      } else if (loudspeakerDevices.isNotEmpty) {
        setLoudspeakerConfig(loudspeakerDevices[0]);
      }
    });
  }
}

import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:surgsmart/generated/l10n.dart';
import 'package:surgsmart/r.g.dart';
import 'package:surgsmart/src/widgets/app_background.widget.dart';

enum DisplayState {
  activate, //激活状态
  //listenStart, //聆听准备
  listening, //聆听中
  //listenEnd, //聆听完成
  success, //正反馈
  fail, //负反馈
}

class SmartVoice extends StatefulWidget {
  final DisplayState state;
  final String? content;
  final int maxContentLength;
  final bool showBottomDynamicEffect;
  final double scale;
  const SmartVoice({
    super.key,
    this.content,
    this.maxContentLength = 15,
    this.showBottomDynamicEffect = true,
    this.scale = 1.0,
    required this.state,
  });

  @override
  State<SmartVoice> createState() {
    return SmartVoiceState();
  }
}

class SmartVoiceState extends State<SmartVoice> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 210.h * widget.scale,
      child: Center(
        child: Stack(
          children: [
            // Positioned(
            //   left: 0,
            //   bottom: 0,
            //   child: Image(
            //     image: R.image.smart_voice_bg(),
            //     fit: BoxFit.fill,
            //   ),
            // ),
            //底部动效
            if (widget.showBottomDynamicEffect)
              Positioned(
                left: 0,
                bottom: -10.h * widget.scale,
                child:
                    widget.state == DisplayState.activate
                        ? FutureBuilder(
                          future: Future.delayed(Duration(milliseconds: 7300)),
                          builder: (context, snapshot) {
                            if (snapshot.connectionState ==
                                ConnectionState.done) {
                              return Image.asset(
                                R.image.voice_bottom_vfx2().assetName,
                                width: 1006.w * widget.scale,
                                height: 308.h * widget.scale,
                                fit: BoxFit.fill,
                                cacheHeight: (308.h * widget.scale).toInt(),
                                cacheWidth: (1006.w * widget.scale).toInt(),
                              );
                            }
                            return Image.asset(
                              R.image.voice_bottom_vfx1().assetName,
                              width: 1006.w * widget.scale,
                              height: 308.h * widget.scale,
                              fit: BoxFit.fill,
                              cacheHeight: (308.h * widget.scale).toInt(),
                              cacheWidth: (1006.w * widget.scale).toInt(),
                            );
                          },
                        )
                        : Image.asset(
                          R.image.voice_bottom_vfx2().assetName,
                          width: 1006.w * widget.scale,
                          height: 308.h * widget.scale,
                          fit: BoxFit.fill,
                          cacheHeight: (308.h * widget.scale).toInt(),
                          cacheWidth: (1006.w * widget.scale).toInt(),
                        ),
              ),
            Positioned(
              left: 52.w * widget.scale,
              bottom: 34.h * widget.scale,
              child: Container(
                padding: EdgeInsets.only(
                  left: 160.w * widget.scale,
                  top: 36.h * widget.scale,
                  right: 40.w * widget.scale,
                  bottom: 36.h * widget.scale,
                ),
                decoration: BoxDecoration(
                  color: Color(0xFF45556D),
                  borderRadius: BorderRadius.circular(100.r * widget.scale),
                ),
                child: Text(
                  getContentByState(),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 46.sp * widget.scale,
                  ),
                ),
              ),
            ),
            Positioned(
              left: 20.w * widget.scale,
              bottom: 10.h * widget.scale,
              child:
                  widget.state == DisplayState.activate
                      ? FutureBuilder(
                        future: Future.delayed(Duration(milliseconds: 600)),
                        builder: (context, snapshot) {
                          if (snapshot.connectionState ==
                              ConnectionState.done) {
                            return getImageByState(DisplayState.listening);
                          }

                          return getImageByState(widget.state);
                        },
                      )
                      : getImageByState(widget.state),
            ),
          ],
        ),
      ),
    );
  }

  Widget getImageByState(DisplayState state) {
    String assetName;
    if (state == DisplayState.activate) {
      assetName = R.image.show_smart().assetName;
    } else if (state == DisplayState.listening) {
      assetName = R.image.await_smart().assetName;
    } else if (state == DisplayState.success) {
      assetName = R.image.listen_success().assetName;
    } else if (state == DisplayState.fail) {
      assetName = R.image.listen_fail().assetName;
    } else {
      assetName = R.image.await_smart().assetName;
    }
    return Image.asset(
      assetName,
      width: 220.r * widget.scale,
      height: 220.r * widget.scale,
      fit: BoxFit.fill,
      cacheHeight: (220.r * widget.scale).toInt(),
      cacheWidth: (220.r * widget.scale).toInt(),
      filterQuality: FilterQuality.none,
    );
  }

  String getContentByState() {
    String? content = widget.content;
    switch (widget.state) {
      case DisplayState.activate:
        content = content ?? "我在";
        break;
      case DisplayState.listening:
        if (content != null) {
          content =
              content.length > widget.maxContentLength
                  ? '...${content.substring(content.length - 14)}'
                  : content;
        } else {
          content = "聆听中...";
        }
        break;
      case DisplayState.fail:
        content = content ?? "抱歉，我不太明白";
        break;
      default:
        if (content != null) {
          content =
              content.length > widget.maxContentLength
                  ? '...${content.substring(content.length - 14)}'
                  : content;
        }
        content = content ?? "";
    }
    return content;
  }
}

class SmartVoiceTips extends StatelessWidget {
  final List<String>? tips;
  const SmartVoiceTips({super.key, this.tips});

  Widget tipsWidget() {
    return ScrollConfiguration(
      behavior: const MaterialScrollBehavior().copyWith(
        physics: const BouncingScrollPhysics(),
        dragDevices: {PointerDeviceKind.touch, PointerDeviceKind.mouse},
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            32.verticalSpace,
            Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Image.asset(
                  R.image.await_smart().assetName,
                  width: 200.w,
                  height: 200.h,
                  fit: BoxFit.fill,
                  cacheHeight: 200.h.toInt(),
                  cacheWidth: 200.w.toInt(),
                ),
                24.horizontalSpace,
                ShaderMask(
                  shaderCallback: (Rect bounds) {
                    return LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [Color(0xFF5CF5EB), Color(0xFF56B3E3)],
                    ).createShader(bounds);
                  },
                  blendMode: BlendMode.srcATop,
                  child: Text('你需要什么帮助？', style: TextStyle(fontSize: 56.sp)),
                ),
              ],
            ),
            56.verticalSpace,
            for (String tip in tips ?? [])
              Container(
                width: 667.w,
                height: 114.h,
                padding: EdgeInsets.only(left: 24.w, right: 24.w),
                margin: EdgeInsets.only(bottom: 36.h),
                decoration: BoxDecoration(
                  color: Color(0x330EC6D2),
                  borderRadius: BorderRadius.circular(57.r),
                ),
                child: Center(
                  child: Text(
                    '“$tip”',
                    maxLines: 1,
                    style: TextStyle(
                      fontSize: 48.sp,
                      color: Colors.white,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget defaultTips() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Image.asset(
          R.image.await_smart().assetName,
          width: 300.w,
          height: 300.h,
          fit: BoxFit.fill,
          cacheHeight: 300.h.toInt(),
          cacheWidth: 300.w.toInt(),
        ),
        64.verticalSpace,
        ShaderMask(
          shaderCallback: (Rect bounds) {
            return LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Color(0xFF5CF5EB), Color(0xFF56B3E3)],
            ).createShader(bounds);
          },
          blendMode: BlendMode.srcATop,
          child: Text(
            textAlign: TextAlign.center,
            tips == null ? S.current.f_9t13qk8l : '呼唤“小睿小睿”',
            style: TextStyle(fontSize: 80.sp),
          ),
        ),
        48.verticalSpace,
        Text(
          tips == null ? S.current.f_9t131ckA : '进行语音控制',
          style: TextStyle(fontSize: 56.sp, color: Colors.white),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return AppBackground(
      width: 941.w,
      radius: 30.r,
      child: tips?.isNotEmpty == true ? tipsWidget() : defaultTips(),
    );
  }
}
